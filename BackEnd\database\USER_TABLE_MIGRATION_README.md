# NeuroIQ Users Table Migration

This directory contains scripts to safely migrate the `neuroiq_users` table to support enhanced RBAC and identity management features.

## 📁 Migration Files

- **`migrate_neuroiq_users_table.sql`** - Main migration SQL script
- **`run_user_table_migration.bat`** - Windows execution script
- **`run_user_table_migration.sh`** - Linux/macOS execution script
- **`USER_TABLE_MIGRATION_README.md`** - This documentation

## 🎯 What This Migration Does

### New Columns Added:
1. **Identity Management:**
   - `niq_user_id` - NeuroIQ unique user identifier
   - `idp_user_id` - Identity Provider user ID (Azure AD, etc.)
   - `source_idp` - Source Identity Provider (azure_ad, okta, etc.)

2. **Enhanced Profile:**
   - `display_name` - User's display name
   - `job_title` - User's job title
   - `department` - User's department
   - `company` - User's company

3. **Account Status:**
   - `account_enabled` - Whether account is enabled
   - `app_user_status` - Application-specific status (active, inactive, pending, suspended)
   - `is_superuser` - Superuser flag
   - `is_staff` - Staff user flag

4. **Security Enhancements:**
   - `password_changed_at` - When password was last changed
   - `failed_login_attempts` - Count of failed login attempts
   - `locked_until` - Account lock expiration time

5. **Audit Trail:**
   - `created_by` - User ID who created this record
   - `updated_by` - User ID who last updated this record
   - `synced_from_idp_at` - Last sync from Identity Provider

### Schema Changes:
- Changed `id` from `INT` to `BIGINT` for better scalability
- Added comprehensive indexing for performance
- Enhanced existing column comments

## 🚀 How to Run the Migration

### Prerequisites
- MySQL 5.7+ or MariaDB 10.3+
- Existing NeuroIQ database with `neuroiq_users` table
- Database user with ALTER, CREATE INDEX privileges

### Option 1: Automated Script (Recommended)

#### For Windows:
```cmd
cd BackEnd\database
run_user_table_migration.bat
```

#### For Linux/macOS:
```bash
cd BackEnd/database
chmod +x run_user_table_migration.sh
./run_user_table_migration.sh
```

### Option 2: Manual Execution
```bash
mysql -u root -p NeuroIQ < migrate_neuroiq_users_table.sql
```

## 🔒 Safety Features

### Automatic Backup
- The script automatically creates a backup of the current table structure
- Backup file format: `neuroiq_users_backup_YYYYMMDD_HHMMSS.sql`

### Transaction Safety
- All changes are wrapped in a transaction
- If any step fails, changes are rolled back

### Non-Destructive
- Uses `ADD COLUMN IF NOT EXISTS` to avoid errors
- Preserves all existing data
- Only adds new columns and indexes

### Data Population
- Automatically generates `niq_user_id` for existing users
- Format: `NIQ_0000000001`, `NIQ_0000000002`, etc.

## 📊 Before and After Comparison

### Before Migration:
```sql
CREATE TABLE neuroiq_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### After Migration:
```sql
CREATE TABLE neuroiq_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    niq_user_id VARCHAR(255) UNIQUE NOT NULL COMMENT 'NeuroIQ unique user identifier',
    idp_user_id VARCHAR(255) COMMENT 'Identity Provider user ID (Azure AD, etc.)',
    source_idp VARCHAR(100) COMMENT 'Source Identity Provider (azure_ad, okta, etc.)',
    -- All existing columns preserved
    -- Plus 15+ new columns for enhanced functionality
);
```

## 🔍 Verification

After running the migration, verify the changes:

```sql
-- Check table structure
DESCRIBE neuroiq_users;

-- Check indexes
SHOW INDEX FROM neuroiq_users;

-- Verify data integrity
SELECT COUNT(*) FROM neuroiq_users;
SELECT COUNT(*) FROM neuroiq_users WHERE niq_user_id IS NOT NULL;
```

## 🚨 Troubleshooting

### Common Issues:

1. **Permission Denied:**
   ```
   ERROR 1142 (42000): ALTER command denied
   ```
   **Solution:** Ensure database user has ALTER privileges

2. **Table Doesn't Exist:**
   ```
   ERROR 1146 (42S02): Table 'NeuroIQ.neuroiq_users' doesn't exist
   ```
   **Solution:** Run the main database setup first

3. **Connection Failed:**
   ```
   ERROR 2003 (HY000): Can't connect to MySQL server
   ```
   **Solution:** Check database configuration in the script

### Recovery:
If migration fails, restore from the automatic backup:
```bash
mysql -u root -p NeuroIQ < neuroiq_users_backup_YYYYMMDD_HHMMSS.sql
```

## ✅ Success Indicators

Migration is successful when you see:
- ✅ "Migration completed successfully!" message
- ✅ Updated table structure displayed
- ✅ All existing data preserved
- ✅ New columns added with proper defaults

## 🔄 Next Steps

After successful migration:
1. Update your application models to use new columns
2. Implement identity provider integration
3. Update user management APIs
4. Test RBAC functionality
5. Update frontend components to use new fields

---

**⚠️ Important:** Always test migrations on a development environment before running on production!
