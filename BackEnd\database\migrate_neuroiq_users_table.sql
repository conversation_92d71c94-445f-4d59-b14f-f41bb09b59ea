-- NeuroIQ Users Table Migration Script
-- This script safely adds or updates columns in the neuroiq_users table
-- Run this script to update the table structure to match the new requirements

USE NeuroIQ;

-- Start transaction for safety
START TRANSACTION;

-- First, let's modify the existing table structure
-- Change id from INT to BIGINT
ALTER TABLE neuroiq_users MODIFY COLUMN id BIGINT AUTO_INCREMENT;

-- Add new columns if they don't exist
-- NeuroIQ unique identifier
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS niq_user_id VARCHAR(255) UNIQUE COMMENT 'NeuroIQ unique user identifier' AFTER id;

-- Identity Provider fields
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS idp_user_id VARCHAR(255) COMMENT 'Identity Provider user ID (Azure AD, etc.)' AFTER niq_user_id;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS source_idp VARCHAR(100) COMMENT 'Source Identity Provider (azure_ad, okta, etc.)' AFTER idp_user_id;

-- Extend existing fields if needed
ALTER TABLE neuroiq_users MODIFY COLUMN email VARCHAR(255) UNIQUE NOT NULL;
ALTER TABLE neuroiq_users MODIFY COLUMN username VARCHAR(100) UNIQUE NOT NULL;

-- Add display_name if it doesn't exist
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS display_name VARCHAR(255) AFTER last_name;

-- Add job and company information
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS job_title VARCHAR(255) AFTER display_name;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS department VARCHAR(255) AFTER job_title;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS company VARCHAR(255) AFTER department;

-- Account status fields
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS account_enabled BOOLEAN DEFAULT TRUE AFTER company;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS app_user_status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'active' AFTER account_enabled;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS is_superuser BOOLEAN DEFAULT FALSE AFTER app_user_status;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS is_staff BOOLEAN DEFAULT FALSE AFTER is_superuser;

-- Update password_hash comment
ALTER TABLE neuroiq_users MODIFY COLUMN password_hash VARCHAR(255) COMMENT 'Hashed password (if using local auth)';

-- Add security fields
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP NULL AFTER last_login;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS failed_login_attempts INT DEFAULT 0 AFTER password_changed_at;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP NULL AFTER failed_login_attempts;

-- Add audit fields
ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS created_by BIGINT COMMENT 'User ID who created this record' AFTER created_at;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS updated_by BIGINT COMMENT 'User ID who last updated this record' AFTER updated_at;

ALTER TABLE neuroiq_users 
ADD COLUMN IF NOT EXISTS synced_from_idp_at TIMESTAMP NULL COMMENT 'Last sync from Identity Provider' AFTER updated_by;

-- Add new indexes
-- Drop existing indexes if they exist to recreate them
DROP INDEX IF EXISTS idx_niq_user_id ON neuroiq_users;
DROP INDEX IF EXISTS idx_idp_user_id ON neuroiq_users;
DROP INDEX IF EXISTS idx_account_status ON neuroiq_users;
DROP INDEX IF EXISTS idx_created_at ON neuroiq_users;

-- Create new indexes
CREATE INDEX idx_niq_user_id ON neuroiq_users (niq_user_id);
CREATE INDEX idx_idp_user_id ON neuroiq_users (idp_user_id);
CREATE INDEX idx_account_status ON neuroiq_users (account_enabled, app_user_status);
CREATE INDEX idx_created_at ON neuroiq_users (created_at);

-- Update existing data to have niq_user_id if it's NULL
-- Generate unique niq_user_id for existing users
UPDATE neuroiq_users 
SET niq_user_id = CONCAT('NIQ_', LPAD(id, 10, '0')) 
WHERE niq_user_id IS NULL;

-- Make niq_user_id NOT NULL after populating existing records
ALTER TABLE neuroiq_users MODIFY COLUMN niq_user_id VARCHAR(255) UNIQUE NOT NULL COMMENT 'NeuroIQ unique user identifier';

-- Commit the transaction
COMMIT;

-- Display the updated table structure
DESCRIBE neuroiq_users;

-- Show success message
SELECT 'NeuroIQ Users table migration completed successfully!' AS Status;
