@echo off
REM NeuroIQ Users Table Migration Script
REM This script safely migrates the neuroiq_users table to the new structure

echo ========================================
echo NeuroIQ Users Table Migration
echo ========================================
echo.

REM Database configuration
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=admin
set DB_NAME=NeuroIQ

echo [INFO] Starting NeuroIQ Users table migration...
echo [INFO] Database: %DB_NAME%
echo [INFO] Host: %DB_HOST%:%DB_PORT%
echo.

REM Test MySQL connection
echo [INFO] Testing MySQL connection...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot connect to MySQL server
    echo [ERROR] Please check your database configuration:
    echo [ERROR] - Host: %DB_HOST%
    echo [ERROR] - Port: %DB_PORT%
    echo [ERROR] - User: %DB_USER%
    echo [ERROR] - Password: %DB_PASSWORD%
    pause
    exit /b 1
)
echo [SUCCESS] MySQL connection successful
echo.

REM Check if database exists
echo [INFO] Checking if database %DB_NAME% exists...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%;" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Database %DB_NAME% does not exist
    echo [ERROR] Please create the database first or run the main setup script
    pause
    exit /b 1
)
echo [SUCCESS] Database %DB_NAME% exists
echo.

REM Check if neuroiq_users table exists
echo [INFO] Checking if neuroiq_users table exists...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE neuroiq_users;" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Table neuroiq_users does not exist
    echo [ERROR] Please run the main database setup first
    pause
    exit /b 1
)
echo [SUCCESS] Table neuroiq_users exists
echo.

REM Create backup
echo [INFO] Creating backup of current table structure...
set BACKUP_FILE=neuroiq_users_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql
set BACKUP_FILE=%BACKUP_FILE: =0%
mysqldump -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% neuroiq_users > "%BACKUP_FILE%" 2>nul
if errorlevel 1 (
    echo [WARNING] Could not create backup, but continuing...
) else (
    echo [SUCCESS] Backup created: %BACKUP_FILE%
)
echo.

REM Run the migration
echo [INFO] Executing migration script...
echo [INFO] This may take a few moments...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "migrate_neuroiq_users_table.sql"
if errorlevel 1 (
    echo [ERROR] Migration failed!
    echo [ERROR] Please check the error messages above
    if exist "%BACKUP_FILE%" (
        echo [INFO] You can restore from backup: %BACKUP_FILE%
    )
    pause
    exit /b 1
)

echo [SUCCESS] Migration completed successfully!
echo.

REM Show the updated table structure
echo [INFO] Updated table structure:
echo ========================================
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE neuroiq_users;"
echo ========================================
echo.

echo [SUCCESS] NeuroIQ Users table migration completed!
echo [INFO] The table now includes all the new columns for RBAC and identity management
echo.
pause
