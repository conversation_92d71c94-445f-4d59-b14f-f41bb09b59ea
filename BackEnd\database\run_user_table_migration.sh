#!/bin/bash

# NeuroIQ Users Table Migration Script
# This script safely migrates the neuroiq_users table to the new structure

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "NeuroIQ Users Table Migration"
echo "========================================"
echo ""

# Database configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="admin"
DB_NAME="NeuroIQ"

print_status "Starting NeuroIQ Users table migration..."
print_status "Database: $DB_NAME"
print_status "Host: $DB_HOST:$DB_PORT"
echo ""

# Test MySQL connection
print_status "Testing MySQL connection..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    print_error "Cannot connect to MySQL server"
    print_error "Please check your database configuration:"
    print_error "- Host: $DB_HOST"
    print_error "- Port: $DB_PORT"
    print_error "- User: $DB_USER"
    print_error "- Password: $DB_PASSWORD"
    exit 1
fi
print_success "MySQL connection successful"
echo ""

# Check if database exists
print_status "Checking if database $DB_NAME exists..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" > /dev/null 2>&1; then
    print_error "Database $DB_NAME does not exist"
    print_error "Please create the database first or run the main setup script"
    exit 1
fi
print_success "Database $DB_NAME exists"
echo ""

# Check if neuroiq_users table exists
print_status "Checking if neuroiq_users table exists..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DESCRIBE neuroiq_users;" > /dev/null 2>&1; then
    print_error "Table neuroiq_users does not exist"
    print_error "Please run the main database setup first"
    exit 1
fi
print_success "Table neuroiq_users exists"
echo ""

# Create backup
print_status "Creating backup of current table structure..."
BACKUP_FILE="neuroiq_users_backup_$(date +%Y%m%d_%H%M%S).sql"
if mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" neuroiq_users > "$BACKUP_FILE" 2>/dev/null; then
    print_success "Backup created: $BACKUP_FILE"
else
    print_warning "Could not create backup, but continuing..."
fi
echo ""

# Run the migration
print_status "Executing migration script..."
print_status "This may take a few moments..."
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "migrate_neuroiq_users_table.sql"; then
    print_success "Migration completed successfully!"
else
    print_error "Migration failed!"
    print_error "Please check the error messages above"
    if [ -f "$BACKUP_FILE" ]; then
        print_status "You can restore from backup: $BACKUP_FILE"
    fi
    exit 1
fi
echo ""

# Show the updated table structure
print_status "Updated table structure:"
echo "========================================"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DESCRIBE neuroiq_users;"
echo "========================================"
echo ""

print_success "NeuroIQ Users table migration completed!"
print_status "The table now includes all the new columns for RBAC and identity management"
echo ""
