[{"D:\\R-NeuroIQ-Admin-Portal\\src\\index.js": "1", "D:\\R-NeuroIQ-Admin-Portal\\src\\App.js": "2", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\Login.js": "3", "D:\\R-NeuroIQ-Admin-Portal\\src\\contexts\\AuthContext.js": "4", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\CreateNewUser.js": "5", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\index.js": "6", "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\authService.js": "7", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\UserRolesPermissions.jsx": "8", "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\api.js": "9", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\RoleDetails.jsx": "10", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\PermissionsGrid.jsx": "11", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\NewRoleModal.jsx": "12", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\RolesList.jsx": "13", "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\UsersTable.jsx": "14", "D:\\R-NeuroIQ-Admin-Portal\\src\\hooks\\useRoles.js": "15", "D:\\R-NeuroIQ-Admin-Portal\\src\\hooks\\usePermissions.js": "16", "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\rbacService.js": "17", "D:\\R-NeuroIQ-Admin-Portal\\src\\data\\permissionsData.js": "18"}, {"size": 254, "mtime": *************, "results": "19", "hashOfConfig": "20"}, {"size": 19512, "mtime": *************, "results": "21", "hashOfConfig": "20"}, {"size": 6111, "mtime": *************, "results": "22", "hashOfConfig": "20"}, {"size": 4202, "mtime": *************, "results": "23", "hashOfConfig": "20"}, {"size": 15977, "mtime": *************, "results": "24", "hashOfConfig": "20"}, {"size": 49, "mtime": *************, "results": "25", "hashOfConfig": "20"}, {"size": 5022, "mtime": *************, "results": "26", "hashOfConfig": "20"}, {"size": 4713, "mtime": *************, "results": "27", "hashOfConfig": "20"}, {"size": 2841, "mtime": *************, "results": "28", "hashOfConfig": "20"}, {"size": 8379, "mtime": 1755617901392, "results": "29", "hashOfConfig": "20"}, {"size": 1676, "mtime": 1755617901392, "results": "30", "hashOfConfig": "20"}, {"size": 3379, "mtime": *************, "results": "31", "hashOfConfig": "20"}, {"size": 916, "mtime": 1755617901392, "results": "32", "hashOfConfig": "20"}, {"size": 3744, "mtime": *************, "results": "33", "hashOfConfig": "20"}, {"size": 4047, "mtime": 1755617901399, "results": "34", "hashOfConfig": "20"}, {"size": 3000, "mtime": 1755617901398, "results": "35", "hashOfConfig": "20"}, {"size": 5869, "mtime": 1755617901402, "results": "36", "hashOfConfig": "20"}, {"size": 6008, "mtime": *************, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19yryz7", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\R-NeuroIQ-Admin-Portal\\src\\index.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\App.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\Login.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\contexts\\AuthContext.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\CreateNewUser.js", ["92"], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\index.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\authService.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\UserRolesPermissions.jsx", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\api.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\RoleDetails.jsx", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\PermissionsGrid.jsx", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\NewRoleModal.jsx", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\RolesList.jsx", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\components\\accounts\\user-roles-permissions\\components\\UsersTable.jsx", ["93"], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\hooks\\useRoles.js", ["94"], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\hooks\\usePermissions.js", ["95"], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\services\\rbacService.js", [], [], "D:\\R-NeuroIQ-Admin-Portal\\src\\data\\permissionsData.js", [], [], {"ruleId": "96", "severity": 1, "message": "97", "line": 63, "column": 44, "nodeType": "98", "messageId": "99", "endLine": 63, "endColumn": 45, "suggestions": "100"}, {"ruleId": "101", "severity": 1, "message": "102", "line": 40, "column": 6, "nodeType": "103", "endLine": 40, "endColumn": 12, "suggestions": "104"}, {"ruleId": "101", "severity": 1, "message": "105", "line": 14, "column": 6, "nodeType": "103", "endLine": 14, "endColumn": 8, "suggestions": "106"}, {"ruleId": "101", "severity": 1, "message": "107", "line": 14, "column": 6, "nodeType": "103", "endLine": 14, "endColumn": 20, "suggestions": "108"}, "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["109", "110"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRoleUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["111"], "React Hook useEffect has a missing dependency: 'fetchRoles'. Either include it or remove the dependency array.", ["112"], "React Hook useEffect has a missing dependency: 'initializePermissions'. Either include it or remove the dependency array.", ["113"], {"messageId": "114", "fix": "115", "desc": "116"}, {"messageId": "117", "fix": "118", "desc": "119"}, {"desc": "120", "fix": "121"}, {"desc": "122", "fix": "123"}, {"desc": "124", "fix": "125"}, "removeEscape", {"range": "126", "text": "127"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "128", "text": "129"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchRoleUsers, role]", {"range": "130", "text": "131"}, "Update the dependencies array to be: [fetchRoles]", {"range": "132", "text": "133"}, "Update the dependencies array to be: [initializePermissions, selectedRole]", {"range": "134", "text": "135"}, [1880, 1881], "", [1880, 1880], "\\", [867, 873], "[fetchRole<PERSON><PERSON><PERSON>, role]", [463, 465], "[fetchRoles]", [442, 456], "[initializePermissions, selectedRole]"]