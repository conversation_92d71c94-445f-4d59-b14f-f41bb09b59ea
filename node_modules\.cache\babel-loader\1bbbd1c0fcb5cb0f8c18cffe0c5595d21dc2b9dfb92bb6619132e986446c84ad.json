{"ast": null, "code": "var _jsxFileName = \"D:\\\\R-NeuroIQ-Admin-Portal\\\\src\\\\components\\\\accounts\\\\user-roles-permissions\\\\components\\\\NewRoleModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewRoleModal = ({\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'Custom Role',\n    description: '',\n    status: 'Active',\n    isDefault: false\n  });\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!formData.name.trim()) {\n      toast.error('Role name is required');\n      return;\n    }\n    onSave(formData);\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Create New Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onCancel,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"modal-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            value: formData.name,\n            onChange: handleChange,\n            className: \"form-input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"type\",\n            value: formData.type,\n            onChange: handleChange,\n            className: \"form-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"System Role\",\n              children: \"System Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Custom Role\",\n              children: \"Custom Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Department Role\",\n              children: \"Department Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"description\",\n            value: formData.description,\n            onChange: handleChange,\n            className: \"form-input\",\n            rows: \"3\",\n            placeholder: \"Describe the purpose and scope of this role\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"status\",\n            value: formData.status,\n            onChange: handleChange,\n            className: \"form-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Inactive\",\n              children: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-checkbox\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              name: \"isDefault\",\n              checked: formData.isDefault,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), \"Set as default role for new users\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-secondary\",\n            onClick: onCancel,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-primary\",\n            children: \"Create Role\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(NewRoleModal, \"Ez0/IU4NfuqQdyvyaxIYGdyT6jk=\");\n_c = NewRoleModal;\nexport default NewRoleModal;\nvar _c;\n$RefreshReg$(_c, \"NewRoleModal\");", "map": {"version": 3, "names": ["React", "useState", "toast", "jsxDEV", "_jsxDEV", "NewRoleModal", "onSave", "onCancel", "_s", "formData", "setFormData", "name", "type", "description", "status", "isDefault", "handleSubmit", "e", "preventDefault", "trim", "error", "handleChange", "value", "checked", "target", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "onChange", "required", "rows", "placeholder", "_c", "$RefreshReg$"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/components/accounts/user-roles-permissions/components/NewRoleModal.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { toast } from 'react-toastify';\n\nconst NewRoleModal = ({ onSave, onCancel }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'Custom Role',\n    description: '',\n    status: 'Active',\n    isDefault: false\n  });\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (!formData.name.trim()) {\n      toast.error('Role name is required');\n      return;\n    }\n    onSave(formData);\n  };\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h3>Create New Role</h3>\n          <button className=\"modal-close\" onClick={onCancel}>&times;</button>\n        </div>\n        \n        <form onSubmit={handleSubmit} className=\"modal-form\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Role Name *</label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className=\"form-input\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"form-label\">Role Type</label>\n            <select\n              name=\"type\"\n              value={formData.type}\n              onChange={handleChange}\n              className=\"form-input\"\n            >\n              <option value=\"System Role\">System Role</option>\n              <option value=\"Custom Role\">Custom Role</option>\n              <option value=\"Department Role\">Department Role</option>\n            </select>\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"form-label\">Description</label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              className=\"form-input\"\n              rows=\"3\"\n              placeholder=\"Describe the purpose and scope of this role\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"form-label\">Status</label>\n            <select\n              name=\"status\"\n              value={formData.status}\n              onChange={handleChange}\n              className=\"form-input\"\n            >\n              <option value=\"Active\">Active</option>\n              <option value=\"Inactive\">Inactive</option>\n            </select>\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"form-checkbox\">\n              <input\n                type=\"checkbox\"\n                name=\"isDefault\"\n                checked={formData.isDefault}\n                onChange={handleChange}\n              />\n              Set as default role for new users\n            </label>\n          </div>\n\n          <div className=\"modal-actions\">\n            <button type=\"button\" className=\"btn-secondary\" onClick={onCancel}>\n              Cancel\n            </button>\n            <button type=\"submit\" className=\"btn-primary\">\n              Create Role\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default NewRoleModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACT,QAAQ,CAACE,IAAI,CAACQ,IAAI,CAAC,CAAC,EAAE;MACzBjB,KAAK,CAACkB,KAAK,CAAC,uBAAuB,CAAC;MACpC;IACF;IACAd,MAAM,CAACG,QAAQ,CAAC;EAClB,CAAC;EAED,MAAMY,YAAY,GAAIJ,CAAC,IAAK;IAC1B,MAAM;MAAEN,IAAI;MAAEW,KAAK;MAAEV,IAAI;MAAEW;IAAQ,CAAC,GAAGN,CAAC,CAACO,MAAM;IAC/Cd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACd,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGW,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,oBACElB,OAAA;IAAKsB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BvB,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BvB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvB,OAAA;UAAAuB,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxB3B,OAAA;UAAQsB,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEzB,QAAS;UAAAoB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAEN3B,OAAA;QAAM6B,QAAQ,EAAEjB,YAAa;QAACU,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAClDvB,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAOsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD3B,OAAA;YACEQ,IAAI,EAAC,MAAM;YACXD,IAAI,EAAC,MAAM;YACXW,KAAK,EAAEb,QAAQ,CAACE,IAAK;YACrBuB,QAAQ,EAAEb,YAAa;YACvBK,SAAS,EAAC,YAAY;YACtBS,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAOsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C3B,OAAA;YACEO,IAAI,EAAC,MAAM;YACXW,KAAK,EAAEb,QAAQ,CAACG,IAAK;YACrBsB,QAAQ,EAAEb,YAAa;YACvBK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBvB,OAAA;cAAQkB,KAAK,EAAC,aAAa;cAAAK,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChD3B,OAAA;cAAQkB,KAAK,EAAC,aAAa;cAAAK,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChD3B,OAAA;cAAQkB,KAAK,EAAC,iBAAiB;cAAAK,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAOsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD3B,OAAA;YACEO,IAAI,EAAC,aAAa;YAClBW,KAAK,EAAEb,QAAQ,CAACI,WAAY;YAC5BqB,QAAQ,EAAEb,YAAa;YACvBK,SAAS,EAAC,YAAY;YACtBU,IAAI,EAAC,GAAG;YACRC,WAAW,EAAC;UAA6C;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAOsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C3B,OAAA;YACEO,IAAI,EAAC,QAAQ;YACbW,KAAK,EAAEb,QAAQ,CAACK,MAAO;YACvBoB,QAAQ,EAAEb,YAAa;YACvBK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBvB,OAAA;cAAQkB,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3B,OAAA;cAAQkB,KAAK,EAAC,UAAU;cAAAK,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBvB,OAAA;YAAOsB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9BvB,OAAA;cACEQ,IAAI,EAAC,UAAU;cACfD,IAAI,EAAC,WAAW;cAChBY,OAAO,EAAEd,QAAQ,CAACM,SAAU;cAC5BmB,QAAQ,EAAEb;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,qCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvB,OAAA;YAAQQ,IAAI,EAAC,QAAQ;YAACc,SAAS,EAAC,eAAe;YAACM,OAAO,EAAEzB,QAAS;YAAAoB,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3B,OAAA;YAAQQ,IAAI,EAAC,QAAQ;YAACc,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9GIH,YAAY;AAAAiC,EAAA,GAAZjC,YAAY;AAgHlB,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}