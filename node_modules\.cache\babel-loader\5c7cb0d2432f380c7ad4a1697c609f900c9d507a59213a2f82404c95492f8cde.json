{"ast": null, "code": "// API Service Layer for R-NeuroIQ Admin Portal\n// Base configuration and utilities for API communication\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nclass ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n    this.token = localStorage.getItem('access_token');\n  }\n\n  // Set authentication token\n  setToken(token) {\n    this.token = token;\n    if (token) {\n      localStorage.setItem('access_token', token);\n    } else {\n      localStorage.removeItem('access_token');\n    }\n  }\n\n  // Get authentication headers\n  getHeaders(contentType = 'application/json') {\n    const headers = {\n      'Content-Type': contentType\n    };\n    if (this.token) {\n      headers['Authorization'] = `Bearer ${this.token}`;\n    }\n    return headers;\n  }\n\n  // Generic request method\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: this.getHeaders(),\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n\n      // Handle different response types\n      let data;\n      const contentType = response.headers.get('content-type');\n      if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n      if (!response.ok) {\n        throw new ApiError(data.detail || data.message || `HTTP ${response.status}`, response.status, data);\n      }\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Network or other errors\n      throw new ApiError(error.message || 'Network error occurred', 0, null);\n    }\n  }\n\n  // HTTP method helpers\n  async get(endpoint, params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    const url = queryString ? `${endpoint}?${queryString}` : endpoint;\n    return this.request(url, {\n      method: 'GET'\n    });\n  }\n  async post(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n  async put(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: JSON.stringify(data)\n    });\n  }\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n\n  // Health check\n  async healthCheck() {\n    return this.get('/health');\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\nexport { ApiService, ApiError, apiService };\nexport default apiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiError", "Error", "constructor", "message", "status", "data", "name", "ApiService", "baseURL", "token", "localStorage", "getItem", "setToken", "setItem", "removeItem", "getHeaders", "contentType", "headers", "request", "endpoint", "options", "url", "config", "response", "fetch", "get", "includes", "json", "text", "ok", "detail", "error", "params", "queryString", "URLSearchParams", "toString", "method", "post", "body", "JSON", "stringify", "put", "delete", "healthCheck", "apiService"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/services/api.js"], "sourcesContent": ["// API Service Layer for R-NeuroIQ Admin Portal\n// Base configuration and utilities for API communication\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nclass ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n    this.token = localStorage.getItem('access_token');\n  }\n\n  // Set authentication token\n  setToken(token) {\n    this.token = token;\n    if (token) {\n      localStorage.setItem('access_token', token);\n    } else {\n      localStorage.removeItem('access_token');\n    }\n  }\n\n  // Get authentication headers\n  getHeaders(contentType = 'application/json') {\n    const headers = {\n      'Content-Type': contentType,\n    };\n\n    if (this.token) {\n      headers['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    return headers;\n  }\n\n  // Generic request method\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: this.getHeaders(),\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      \n      // Handle different response types\n      let data;\n      const contentType = response.headers.get('content-type');\n      \n      if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n\n      if (!response.ok) {\n        throw new ApiError(\n          data.detail || data.message || `HTTP ${response.status}`,\n          response.status,\n          data\n        );\n      }\n\n      return data;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      \n      // Network or other errors\n      throw new ApiError(\n        error.message || 'Network error occurred',\n        0,\n        null\n      );\n    }\n  }\n\n  // HTTP method helpers\n  async get(endpoint, params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    const url = queryString ? `${endpoint}?${queryString}` : endpoint;\n    \n    return this.request(url, {\n      method: 'GET',\n    });\n  }\n\n  async post(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async put(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE',\n    });\n  }\n\n  // Health check\n  async healthCheck() {\n    return this.get('/health');\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\n\nexport { ApiService, ApiError, apiService };\nexport default apiService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,QAAQ,SAASC,KAAK,CAAC;EAC3BC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE;IACjC,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACG,IAAI,GAAG,UAAU;IACtB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;AACF;AAEA,MAAME,UAAU,CAAC;EACfL,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACM,OAAO,GAAGZ,YAAY;IAC3B,IAAI,CAACa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EACnD;;EAEA;EACAC,QAAQA,CAACH,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAIA,KAAK,EAAE;MACTC,YAAY,CAACG,OAAO,CAAC,cAAc,EAAEJ,KAAK,CAAC;IAC7C,CAAC,MAAM;MACLC,YAAY,CAACI,UAAU,CAAC,cAAc,CAAC;IACzC;EACF;;EAEA;EACAC,UAAUA,CAACC,WAAW,GAAG,kBAAkB,EAAE;IAC3C,MAAMC,OAAO,GAAG;MACd,cAAc,EAAED;IAClB,CAAC;IAED,IAAI,IAAI,CAACP,KAAK,EAAE;MACdQ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAACR,KAAK,EAAE;IACnD;IAEA,OAAOQ,OAAO;EAChB;;EAEA;EACA,MAAMC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACb,OAAO,GAAGW,QAAQ,EAAE;IACxC,MAAMG,MAAM,GAAG;MACbL,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC,CAAC;MAC1B,GAAGK;IACL,CAAC;IAED,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEC,MAAM,CAAC;;MAEzC;MACA,IAAIjB,IAAI;MACR,MAAMW,WAAW,GAAGO,QAAQ,CAACN,OAAO,CAACQ,GAAG,CAAC,cAAc,CAAC;MAExD,IAAIT,WAAW,IAAIA,WAAW,CAACU,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC3DrB,IAAI,GAAG,MAAMkB,QAAQ,CAACI,IAAI,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLtB,IAAI,GAAG,MAAMkB,QAAQ,CAACK,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAI7B,QAAQ,CAChBK,IAAI,CAACyB,MAAM,IAAIzB,IAAI,CAACF,OAAO,IAAI,QAAQoB,QAAQ,CAACnB,MAAM,EAAE,EACxDmB,QAAQ,CAACnB,MAAM,EACfC,IACF,CAAC;MACH;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACd,IAAIA,KAAK,YAAY/B,QAAQ,EAAE;QAC7B,MAAM+B,KAAK;MACb;;MAEA;MACA,MAAM,IAAI/B,QAAQ,CAChB+B,KAAK,CAAC5B,OAAO,IAAI,wBAAwB,EACzC,CAAC,EACD,IACF,CAAC;IACH;EACF;;EAEA;EACA,MAAMsB,GAAGA,CAACN,QAAQ,EAAEa,MAAM,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,MAAMd,GAAG,GAAGY,WAAW,GAAG,GAAGd,QAAQ,IAAIc,WAAW,EAAE,GAAGd,QAAQ;IAEjE,OAAO,IAAI,CAACD,OAAO,CAACG,GAAG,EAAE;MACvBe,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAMC,IAAIA,CAAClB,QAAQ,EAAEd,IAAI,GAAG,CAAC,CAAC,EAAE;IAC9B,OAAO,IAAI,CAACa,OAAO,CAACC,QAAQ,EAAE;MAC5BiB,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnC,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMoC,GAAGA,CAACtB,QAAQ,EAAEd,IAAI,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO,IAAI,CAACa,OAAO,CAACC,QAAQ,EAAE;MAC5BiB,MAAM,EAAE,KAAK;MACbE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnC,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMqC,MAAMA,CAACvB,QAAQ,EAAE;IACrB,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ,EAAE;MAC5BiB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClB,GAAG,CAAC,SAAS,CAAC;EAC5B;AACF;;AAEA;AACA,MAAMmB,UAAU,GAAG,IAAIrC,UAAU,CAAC,CAAC;AAEnC,SAASA,UAAU,EAAEP,QAAQ,EAAE4C,UAAU;AACzC,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}