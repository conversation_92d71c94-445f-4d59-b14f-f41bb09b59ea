{"ast": null, "code": "var _jsxFileName = \"D:\\\\R-NeuroIQ-Admin-Portal\\\\src\\\\components\\\\accounts\\\\user-roles-permissions\\\\components\\\\RolesList.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RolesList = ({\n  roles,\n  selectedRole,\n  onRoleSelect,\n  onCreateRole\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"roles-panel\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"panel-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"panel-title\",\n        children: \"Roles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: onCreateRole,\n        children: \"+ New Role\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"roles-list\",\n      children: roles.map(role => {\n        var _role$description;\n        return /*#__PURE__*/_jsxDEV(\"li\", {\n          className: `role-item ${(selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.id) === role.id ? 'active' : ''}`,\n          onClick: () => onRoleSelect(role),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"role-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: role.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [(_role$description = role.description) === null || _role$description === void 0 ? void 0 : _role$description.substring(0, 50), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"role-badge\",\n            children: [role.userCount, \" users\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, role.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = RolesList;\nexport default RolesList;\nvar _c;\n$RefreshReg$(_c, \"RolesList\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "RolesList", "roles", "selectedR<PERSON>", "onRoleSelect", "onCreateRole", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "role", "_role$description", "id", "name", "description", "substring", "userCount", "_c", "$RefreshReg$"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/components/accounts/user-roles-permissions/components/RolesList.jsx"], "sourcesContent": ["import React from 'react';\n\nconst RolesList = ({ roles, selectedRole, onRoleSelect, onCreateRole }) => {\n  return (\n    <div className=\"roles-panel\">\n      <div className=\"panel-header\">\n        <h2 className=\"panel-title\">Roles</h2>\n        <button className=\"btn-primary\" onClick={onCreateRole}>\n          + New Role\n        </button>\n      </div>\n      \n      <ul className=\"roles-list\">\n        {roles.map((role) => (\n          <li\n            key={role.id}\n            className={`role-item ${selectedRole?.id === role.id ? 'active' : ''}`}\n            onClick={() => onRoleSelect(role)}\n          >\n            <div className=\"role-info\">\n              <h4>{role.name}</h4>\n              <p>{role.description?.substring(0, 50)}...</p>\n            </div>\n            <span className=\"role-badge\">{role.userCount} users</span>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n};\n\nexport default RolesList;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EACzE,oBACEL,OAAA;IAAKM,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BP,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BP,OAAA;QAAIM,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCX,OAAA;QAAQM,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEP,YAAa;QAAAE,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENX,OAAA;MAAIM,SAAS,EAAC,YAAY;MAAAC,QAAA,EACvBL,KAAK,CAACW,GAAG,CAAEC,IAAI;QAAA,IAAAC,iBAAA;QAAA,oBACdf,OAAA;UAEEM,SAAS,EAAE,aAAa,CAAAH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,EAAE,MAAKF,IAAI,CAACE,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvEJ,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACU,IAAI,CAAE;UAAAP,QAAA,gBAElCP,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBP,OAAA;cAAAO,QAAA,EAAKO,IAAI,CAACG;YAAI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpBX,OAAA;cAAAO,QAAA,IAAAQ,iBAAA,GAAID,IAAI,CAACI,WAAW,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNX,OAAA;YAAMM,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEO,IAAI,CAACM,SAAS,EAAC,QAAM;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GARrDG,IAAI,CAACE,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CAAC;MAAA,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACU,EAAA,GA3BIpB,SAAS;AA6Bf,eAAeA,SAAS;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}