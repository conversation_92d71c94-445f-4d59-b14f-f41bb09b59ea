{"ast": null, "code": "var _jsxFileName = \"D:\\\\R-NeuroIQ-Admin-Portal\\\\src\\\\components\\\\accounts\\\\user-roles-permissions\\\\components\\\\UsersTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UsersTable = ({\n  role\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // Mock users data\n  const mockUsers = [{\n    id: 1,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    department: 'Engineering',\n    status: 'Active',\n    lastActive: '2 hours ago',\n    avatar: 'JD'\n  }, {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    department: 'Operations',\n    status: 'Active',\n    lastActive: '1 day ago',\n    avatar: 'J<PERSON>'\n  }, {\n    id: 3,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    department: 'Marketing',\n    status: 'Inactive',\n    lastActive: '1 week ago',\n    avatar: 'BW'\n  }];\n  useEffect(() => {\n    fetchRoleUsers();\n  }, [role]);\n  const fetchRoleUsers = async () => {\n    setLoading(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      const filteredUsers = mockUsers.slice(0, Math.min(role.userCount, 3));\n      setUsers(filteredUsers);\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRemoveUser = userId => {\n    if (window.confirm('Are you sure you want to remove this user from the role?')) {\n      setUsers(prev => prev.filter(user => user.id !== userId));\n    }\n  };\n  const handleAssignUsers = () => {\n    console.log('Open user assignment modal');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"users-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"users-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"panel-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"panel-title\",\n        children: [\"Users with \", role.name, \" Role\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: handleAssignUsers,\n        children: \"Assign Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), users.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No users assigned to this role yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: handleAssignUsers,\n        children: \"Assign Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"users-table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Department\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Last Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-avatar\",\n                children: user.avatar\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 500\n                  },\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.department\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status-badge ${user.status.toLowerCase() === 'active' ? 'status-active' : 'status-inactive'}`,\n              children: user.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.lastActive\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-secondary\",\n              style: {\n                padding: '4px 8px',\n                fontSize: '12px'\n              },\n              onClick: () => handleRemoveUser(user.id),\n              children: \"Remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this)]\n        }, user.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(UsersTable, \"qCTuiBwHZyEgUz10B+A8oTYLc14=\");\n_c = UsersTable;\nexport default UsersTable;\nvar _c;\n$RefreshReg$(_c, \"UsersTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UsersTable", "role", "_s", "users", "setUsers", "loading", "setLoading", "mockUsers", "id", "name", "email", "department", "status", "lastActive", "avatar", "fetchRoleUsers", "Promise", "resolve", "setTimeout", "filteredUsers", "slice", "Math", "min", "userCount", "error", "console", "handleRemoveUser", "userId", "window", "confirm", "prev", "filter", "user", "handleAssignUsers", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "style", "fontWeight", "toLowerCase", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/components/accounts/user-roles-permissions/components/UsersTable.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UsersTable = ({ role }) => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // Mock users data\n  const mockUsers = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Engineering',\n      status: 'Active',\n      lastActive: '2 hours ago',\n      avatar: 'JD'\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Operations',\n      status: 'Active',\n      lastActive: '1 day ago',\n      avatar: 'JS'\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Marketing',\n      status: 'Inactive',\n      lastActive: '1 week ago',\n      avatar: 'BW'\n    }\n  ];\n\n  useEffect(() => {\n    fetchRoleUsers();\n  }, [role]);\n\n  const fetchRoleUsers = async () => {\n    setLoading(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      const filteredUsers = mockUsers.slice(0, Math.min(role.userCount, 3));\n      setUsers(filteredUsers);\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveUser = (userId) => {\n    if (window.confirm('Are you sure you want to remove this user from the role?')) {\n      setUsers(prev => prev.filter(user => user.id !== userId));\n    }\n  };\n\n  const handleAssignUsers = () => {\n    console.log('Open user assignment modal');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"users-section\">\n        <div className=\"loading-spinner\">Loading users...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"users-section\">\n      <div className=\"panel-header\">\n        <h2 className=\"panel-title\">Users with {role.name} Role</h2>\n        <button className=\"btn-primary\" onClick={handleAssignUsers}>\n          Assign Users\n        </button>\n      </div>\n      \n      {users.length === 0 ? (\n        <div className=\"empty-state\">\n          <p>No users assigned to this role yet.</p>\n          <button className=\"btn-primary\" onClick={handleAssignUsers}>\n            Assign Users\n          </button>\n        </div>\n      ) : (\n        <table className=\"users-table\">\n          <thead>\n            <tr>\n              <th>User</th>\n              <th>Email</th>\n              <th>Department</th>\n              <th>Status</th>\n              <th>Last Active</th>\n              <th>Actions</th>\n            </tr>\n          </thead>\n          <tbody>\n            {users.map((user) => (\n              <tr key={user.id}>\n                <td>\n                  <div className=\"user-info\">\n                    <div className=\"user-avatar\">{user.avatar}</div>\n                    <div>\n                      <div style={{ fontWeight: 500 }}>{user.name}</div>\n                    </div>\n                  </div>\n                </td>\n                <td>{user.email}</td>\n                <td>{user.department}</td>\n                <td>\n                  <span className={`status-badge ${user.status.toLowerCase() === 'active' ? 'status-active' : 'status-inactive'}`}>\n                    {user.status}\n                  </span>\n                </td>\n                <td>{user.lastActive}</td>\n                <td>\n                  <button\n                    className=\"btn-secondary\"\n                    style={{ padding: '4px 8px', fontSize: '12px' }}\n                    onClick={() => handleRemoveUser(user.id)}\n                  >\n                    Remove\n                  </button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      )}\n    </div>\n  );\n};\n\nexport default UsersTable;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMW,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,sBAAsB;IAC7BC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,wBAAwB;IAC/BC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,WAAW;IACvBC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,wBAAwB;IAC/BC,UAAU,EAAE,WAAW;IACvBC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE;EACV,CAAC,CACF;EAEDjB,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;EAEV,MAAMc,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MACtD,MAAME,aAAa,GAAGZ,SAAS,CAACa,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACrB,IAAI,CAACsB,SAAS,EAAE,CAAC,CAAC,CAAC;MACrEnB,QAAQ,CAACe,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,gBAAgB,GAAIC,MAAM,IAAK;IACnC,IAAIC,MAAM,CAACC,OAAO,CAAC,0DAA0D,CAAC,EAAE;MAC9EzB,QAAQ,CAAC0B,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxB,EAAE,KAAKmB,MAAM,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;EAC3C,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKoC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BrC,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,oBACEzC,OAAA;IAAKoC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BrC,OAAA;MAAKoC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrC,OAAA;QAAIoC,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,aAAW,EAACnC,IAAI,CAACQ,IAAI,EAAC,OAAK;MAAA;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5DzC,OAAA;QAAQoC,SAAS,EAAC,aAAa;QAACM,OAAO,EAAER,iBAAkB;QAAAG,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELrC,KAAK,CAACuC,MAAM,KAAK,CAAC,gBACjB3C,OAAA;MAAKoC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrC,OAAA;QAAAqC,QAAA,EAAG;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1CzC,OAAA;QAAQoC,SAAS,EAAC,aAAa;QAACM,OAAO,EAAER,iBAAkB;QAAAG,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENzC,OAAA;MAAOoC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC5BrC,OAAA;QAAAqC,QAAA,eACErC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbzC,OAAA;YAAAqC,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdzC,OAAA;YAAAqC,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBzC,OAAA;YAAAqC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfzC,OAAA;YAAAqC,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBzC,OAAA;YAAAqC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRzC,OAAA;QAAAqC,QAAA,EACGjC,KAAK,CAACwC,GAAG,CAAEX,IAAI,iBACdjC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,eACErC,OAAA;cAAKoC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrC,OAAA;gBAAKoC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEJ,IAAI,CAAClB;cAAM;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDzC,OAAA;gBAAAqC,QAAA,eACErC,OAAA;kBAAK6C,KAAK,EAAE;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAT,QAAA,EAAEJ,IAAI,CAACvB;gBAAI;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACLzC,OAAA;YAAAqC,QAAA,EAAKJ,IAAI,CAACtB;UAAK;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBzC,OAAA;YAAAqC,QAAA,EAAKJ,IAAI,CAACrB;UAAU;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BzC,OAAA;YAAAqC,QAAA,eACErC,OAAA;cAAMoC,SAAS,EAAE,gBAAgBH,IAAI,CAACpB,MAAM,CAACkC,WAAW,CAAC,CAAC,KAAK,QAAQ,GAAG,eAAe,GAAG,iBAAiB,EAAG;cAAAV,QAAA,EAC7GJ,IAAI,CAACpB;YAAM;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLzC,OAAA;YAAAqC,QAAA,EAAKJ,IAAI,CAACnB;UAAU;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BzC,OAAA;YAAAqC,QAAA,eACErC,OAAA;cACEoC,SAAS,EAAC,eAAe;cACzBS,KAAK,EAAE;gBAAEG,OAAO,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAChDP,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACM,IAAI,CAACxB,EAAE,CAAE;cAAA4B,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAzBER,IAAI,CAACxB,EAAE;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BZ,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CArIIF,UAAU;AAAAiD,EAAA,GAAVjD,UAAU;AAuIhB,eAAeA,UAAU;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}