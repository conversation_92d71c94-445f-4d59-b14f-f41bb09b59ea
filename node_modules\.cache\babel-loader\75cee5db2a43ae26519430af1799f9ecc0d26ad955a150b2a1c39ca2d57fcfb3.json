{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { DEFAULT_ROLES } from '../data/permissionsData';\nimport rbacService from '../services/rbacService';\nimport { toast } from 'react-toastify';\nexport const useRoles = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchRoles();\n  }, []);\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const result = await rbacService.getAllRoles();\n      if (result.success) {\n        setRoles(result.roles);\n        setError(null);\n\n        // Set first role as selected if none selected\n        if (!selectedRole && result.roles.length > 0) {\n          setSelectedRole(result.roles[0]);\n        }\n      } else {\n        setError(result.error);\n        console.error('Failed to fetch roles:', result.error);\n\n        // Fallback to mock data if API fails\n        setRoles(DEFAULT_ROLES);\n        if (!selectedRole && DEFAULT_ROLES.length > 0) {\n          setSelectedRole(DEFAULT_ROLES[0]);\n        }\n      }\n    } catch (err) {\n      setError('Failed to fetch roles');\n      console.error('Fetch roles error:', err);\n\n      // Fallback to mock data\n      setRoles(DEFAULT_ROLES);\n      if (!selectedRole && DEFAULT_ROLES.length > 0) {\n        setSelectedRole(DEFAULT_ROLES[0]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const createRole = async roleData => {\n    try {\n      const result = await rbacService.createRole(roleData);\n      if (result.success) {\n        // Add the new role to the list\n        setRoles(prev => [...prev, result.role]);\n        toast.success(result.message || 'Role created successfully');\n        return result.role;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to create role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to create role');\n      console.error('Create role error:', err);\n      throw err;\n    }\n  };\n  const updateRole = async (roleId, roleData) => {\n    try {\n      const result = await rbacService.updateRole(roleId, roleData);\n      if (result.success) {\n        // Update the role in the list\n        setRoles(prev => prev.map(role => role.id === roleId ? result.role : role));\n\n        // Update selected role if it's the one being updated\n        if (selectedRole && selectedRole.id === roleId) {\n          setSelectedRole(result.role);\n        }\n        toast.success(result.message || 'Role updated successfully');\n        return result.role;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to update role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to update role');\n      console.error('Update role error:', err);\n      throw err;\n    }\n  };\n  const deleteRole = async roleId => {\n    try {\n      const result = await rbacService.deleteRole(roleId);\n      if (result.success) {\n        // Remove the role from the list\n        setRoles(prev => prev.filter(role => role.id !== roleId));\n\n        // Clear selected role if it's the one being deleted\n        if (selectedRole && selectedRole.id === roleId) {\n          const remainingRoles = roles.filter(role => role.id !== roleId);\n          setSelectedRole(remainingRoles.length > 0 ? remainingRoles[0] : null);\n        }\n        toast.success(result.message || 'Role deleted successfully');\n        return true;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to delete role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to delete role');\n      console.error('Delete role error:', err);\n      throw err;\n    }\n  };\n  return {\n    roles,\n    selectedRole,\n    setSelectedRole,\n    loading,\n    error,\n    createRole,\n    updateRole,\n    deleteRole,\n    fetchRoles\n  };\n};\n_s(useRoles, \"gTTU7MfTiDREL6opY34xThFU/iI=\");", "map": {"version": 3, "names": ["useState", "useEffect", "DEFAULT_ROLES", "rbacService", "toast", "useRoles", "_s", "roles", "setRoles", "selectedR<PERSON>", "setSelectedRole", "loading", "setLoading", "error", "setError", "fetchRoles", "result", "getAllRoles", "success", "length", "console", "err", "createRole", "roleData", "prev", "role", "message", "Error", "updateRole", "roleId", "map", "id", "deleteRole", "filter", "remainingRoles"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/hooks/useRoles.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { DEFAULT_ROLES } from '../data/permissionsData';\nimport rbacService from '../services/rbacService';\nimport { toast } from 'react-toastify';\n\nexport const useRoles = () => {\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchRoles();\n  }, []);\n\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const result = await rbacService.getAllRoles();\n\n      if (result.success) {\n        setRoles(result.roles);\n        setError(null);\n\n        // Set first role as selected if none selected\n        if (!selectedRole && result.roles.length > 0) {\n          setSelectedRole(result.roles[0]);\n        }\n      } else {\n        setError(result.error);\n        console.error('Failed to fetch roles:', result.error);\n\n        // Fallback to mock data if API fails\n        setRoles(DEFAULT_ROLES);\n        if (!selectedRole && DEFAULT_ROLES.length > 0) {\n          setSelectedRole(DEFAULT_ROLES[0]);\n        }\n      }\n    } catch (err) {\n      setError('Failed to fetch roles');\n      console.error('Fetch roles error:', err);\n\n      // Fallback to mock data\n      setRoles(DEFAULT_ROLES);\n      if (!selectedRole && DEFAULT_ROLES.length > 0) {\n        setSelectedRole(DEFAULT_ROLES[0]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createRole = async (roleData) => {\n    try {\n      const result = await rbacService.createRole(roleData);\n\n      if (result.success) {\n        // Add the new role to the list\n        setRoles(prev => [...prev, result.role]);\n        toast.success(result.message || 'Role created successfully');\n        return result.role;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to create role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to create role');\n      console.error('Create role error:', err);\n      throw err;\n    }\n  };\n\n  const updateRole = async (roleId, roleData) => {\n    try {\n      const result = await rbacService.updateRole(roleId, roleData);\n\n      if (result.success) {\n        // Update the role in the list\n        setRoles(prev => prev.map(role =>\n          role.id === roleId ? result.role : role\n        ));\n\n        // Update selected role if it's the one being updated\n        if (selectedRole && selectedRole.id === roleId) {\n          setSelectedRole(result.role);\n        }\n\n        toast.success(result.message || 'Role updated successfully');\n        return result.role;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to update role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to update role');\n      console.error('Update role error:', err);\n      throw err;\n    }\n  };\n\n  const deleteRole = async (roleId) => {\n    try {\n      const result = await rbacService.deleteRole(roleId);\n\n      if (result.success) {\n        // Remove the role from the list\n        setRoles(prev => prev.filter(role => role.id !== roleId));\n\n        // Clear selected role if it's the one being deleted\n        if (selectedRole && selectedRole.id === roleId) {\n          const remainingRoles = roles.filter(role => role.id !== roleId);\n          setSelectedRole(remainingRoles.length > 0 ? remainingRoles[0] : null);\n        }\n\n        toast.success(result.message || 'Role deleted successfully');\n        return true;\n      } else {\n        setError(result.error);\n        toast.error(result.error || 'Failed to delete role');\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError('Failed to delete role');\n      console.error('Delete role error:', err);\n      throw err;\n    }\n  };\n\n  return {\n    roles,\n    selectedRole,\n    setSelectedRole,\n    loading,\n    error,\n    createRole,\n    updateRole,\n    deleteRole,\n    fetchRoles\n  };\n};"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdc,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMb,WAAW,CAACc,WAAW,CAAC,CAAC;MAE9C,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBV,QAAQ,CAACQ,MAAM,CAACT,KAAK,CAAC;QACtBO,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAI,CAACL,YAAY,IAAIO,MAAM,CAACT,KAAK,CAACY,MAAM,GAAG,CAAC,EAAE;UAC5CT,eAAe,CAACM,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACLO,QAAQ,CAACE,MAAM,CAACH,KAAK,CAAC;QACtBO,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEG,MAAM,CAACH,KAAK,CAAC;;QAErD;QACAL,QAAQ,CAACN,aAAa,CAAC;QACvB,IAAI,CAACO,YAAY,IAAIP,aAAa,CAACiB,MAAM,GAAG,CAAC,EAAE;UAC7CT,eAAe,CAACR,aAAa,CAAC,CAAC,CAAC,CAAC;QACnC;MACF;IACF,CAAC,CAAC,OAAOmB,GAAG,EAAE;MACZP,QAAQ,CAAC,uBAAuB,CAAC;MACjCM,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;;MAExC;MACAb,QAAQ,CAACN,aAAa,CAAC;MACvB,IAAI,CAACO,YAAY,IAAIP,aAAa,CAACiB,MAAM,GAAG,CAAC,EAAE;QAC7CT,eAAe,CAACR,aAAa,CAAC,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,SAAS;MACRU,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC,IAAI;MACF,MAAMP,MAAM,GAAG,MAAMb,WAAW,CAACmB,UAAU,CAACC,QAAQ,CAAC;MAErD,IAAIP,MAAM,CAACE,OAAO,EAAE;QAClB;QACAV,QAAQ,CAACgB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,MAAM,CAACS,IAAI,CAAC,CAAC;QACxCrB,KAAK,CAACc,OAAO,CAACF,MAAM,CAACU,OAAO,IAAI,2BAA2B,CAAC;QAC5D,OAAOV,MAAM,CAACS,IAAI;MACpB,CAAC,MAAM;QACLX,QAAQ,CAACE,MAAM,CAACH,KAAK,CAAC;QACtBT,KAAK,CAACS,KAAK,CAACG,MAAM,CAACH,KAAK,IAAI,uBAAuB,CAAC;QACpD,MAAM,IAAIc,KAAK,CAACX,MAAM,CAACH,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZP,QAAQ,CAAC,uBAAuB,CAAC;MACjCM,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;MACxC,MAAMA,GAAG;IACX;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAAA,CAAOC,MAAM,EAAEN,QAAQ,KAAK;IAC7C,IAAI;MACF,MAAMP,MAAM,GAAG,MAAMb,WAAW,CAACyB,UAAU,CAACC,MAAM,EAAEN,QAAQ,CAAC;MAE7D,IAAIP,MAAM,CAACE,OAAO,EAAE;QAClB;QACAV,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACM,GAAG,CAACL,IAAI,IAC5BA,IAAI,CAACM,EAAE,KAAKF,MAAM,GAAGb,MAAM,CAACS,IAAI,GAAGA,IACrC,CAAC,CAAC;;QAEF;QACA,IAAIhB,YAAY,IAAIA,YAAY,CAACsB,EAAE,KAAKF,MAAM,EAAE;UAC9CnB,eAAe,CAACM,MAAM,CAACS,IAAI,CAAC;QAC9B;QAEArB,KAAK,CAACc,OAAO,CAACF,MAAM,CAACU,OAAO,IAAI,2BAA2B,CAAC;QAC5D,OAAOV,MAAM,CAACS,IAAI;MACpB,CAAC,MAAM;QACLX,QAAQ,CAACE,MAAM,CAACH,KAAK,CAAC;QACtBT,KAAK,CAACS,KAAK,CAACG,MAAM,CAACH,KAAK,IAAI,uBAAuB,CAAC;QACpD,MAAM,IAAIc,KAAK,CAACX,MAAM,CAACH,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZP,QAAQ,CAAC,uBAAuB,CAAC;MACjCM,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;MACxC,MAAMA,GAAG;IACX;EACF,CAAC;EAED,MAAMW,UAAU,GAAG,MAAOH,MAAM,IAAK;IACnC,IAAI;MACF,MAAMb,MAAM,GAAG,MAAMb,WAAW,CAAC6B,UAAU,CAACH,MAAM,CAAC;MAEnD,IAAIb,MAAM,CAACE,OAAO,EAAE;QAClB;QACAV,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACS,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKF,MAAM,CAAC,CAAC;;QAEzD;QACA,IAAIpB,YAAY,IAAIA,YAAY,CAACsB,EAAE,KAAKF,MAAM,EAAE;UAC9C,MAAMK,cAAc,GAAG3B,KAAK,CAAC0B,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKF,MAAM,CAAC;UAC/DnB,eAAe,CAACwB,cAAc,CAACf,MAAM,GAAG,CAAC,GAAGe,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACvE;QAEA9B,KAAK,CAACc,OAAO,CAACF,MAAM,CAACU,OAAO,IAAI,2BAA2B,CAAC;QAC5D,OAAO,IAAI;MACb,CAAC,MAAM;QACLZ,QAAQ,CAACE,MAAM,CAACH,KAAK,CAAC;QACtBT,KAAK,CAACS,KAAK,CAACG,MAAM,CAACH,KAAK,IAAI,uBAAuB,CAAC;QACpD,MAAM,IAAIc,KAAK,CAACX,MAAM,CAACH,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZP,QAAQ,CAAC,uBAAuB,CAAC;MACjCM,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;MACxC,MAAMA,GAAG;IACX;EACF,CAAC;EAED,OAAO;IACLd,KAAK;IACLE,YAAY;IACZC,eAAe;IACfC,OAAO;IACPE,KAAK;IACLS,UAAU;IACVM,UAAU;IACVI,UAAU;IACVjB;EACF,CAAC;AACH,CAAC;AAACT,EAAA,CAxIWD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}