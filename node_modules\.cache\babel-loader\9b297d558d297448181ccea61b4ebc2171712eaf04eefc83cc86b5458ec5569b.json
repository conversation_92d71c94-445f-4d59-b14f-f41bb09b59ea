{"ast": null, "code": "var _jsxFileName = \"D:\\\\R-NeuroIQ-Admin-Portal\\\\src\\\\components\\\\accounts\\\\user-roles-permissions\\\\components\\\\RoleDetails.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleDetails = ({\n  role,\n  isEditing,\n  onEdit,\n  onSave,\n  onCancel,\n  onDelete\n}) => {\n  _s();\n  const [formData, setFormData] = useState(role);\n  useEffect(() => {\n    setFormData(role);\n  }, [role]);\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSave(formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"role-details\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: [role.name, \" Role\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            value: formData.name,\n            onChange: handleChange,\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"type\",\n            value: formData.type,\n            onChange: handleChange,\n            className: \"form-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"System Role\",\n              children: \"System Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Custom Role\",\n              children: \"Custom Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Department Role\",\n              children: \"Department Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"description\",\n          value: formData.description,\n          onChange: handleChange,\n          className: \"form-input\",\n          rows: \"2\",\n          placeholder: \"Detailed explanation of role purpose\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"status\",\n            value: formData.status,\n            onChange: handleChange,\n            className: \"form-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Inactive\",\n              children: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Deprecated\",\n              children: \"Deprecated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"User Count\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData.userCount,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Default Role\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"isDefault\",\n            value: formData.isDefault ? 'Yes' : 'No',\n            onChange: e => handleChange({\n              target: {\n                name: 'isDefault',\n                value: e.target.value === 'Yes',\n                type: 'checkbox',\n                checked: e.target.value === 'Yes'\n              }\n            }),\n            className: \"form-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"No\",\n              children: \"No\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Yes\",\n              children: \"Yes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"actions-bar\",\n        style: {\n          marginTop: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-secondary\",\n            onClick: onCancel,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-primary\",\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.name,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Role Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.type,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: role.description,\n          className: \"form-input\",\n          rows: \"2\",\n          readOnly: true,\n          style: {\n            background: '#f9fafb'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.status,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"User Count\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.userCount,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Default Role\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.isDefault ? 'Yes' : 'No',\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Created Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.createdDate,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Created By\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.createdBy,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Last Modified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.lastModified,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Modified By\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: role.modifiedBy,\n            className: \"form-input\",\n            readOnly: true,\n            style: {\n              background: '#f9fafb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"actions-bar\",\n        style: {\n          marginTop: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-secondary\",\n            onClick: onEdit,\n            children: \"Edit Role\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-danger\",\n          onClick: onDelete,\n          children: \"Delete Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleDetails, \"63z0pm649Pif9q/IriTzL2MlCvg=\");\n_c = RoleDetails;\nexport default RoleDetails;\nvar _c;\n$RefreshReg$(_c, \"RoleDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "RoleDetails", "role", "isEditing", "onEdit", "onSave", "onCancel", "onDelete", "_s", "formData", "setFormData", "handleChange", "e", "name", "value", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "style", "display", "gridTemplateColumns", "gap", "onChange", "description", "rows", "placeholder", "status", "userCount", "readOnly", "background", "isDefault", "marginTop", "onClick", "createdDate", "created<PERSON>y", "lastModified", "modifiedBy", "_c", "$RefreshReg$"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/components/accounts/user-roles-permissions/components/RoleDetails.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst RoleDetails = ({ role, isEditing, onEdit, onSave, onCancel, onDelete }) => {\n  const [formData, setFormData] = useState(role);\n\n  useEffect(() => {\n    setFormData(role);\n  }, [role]);\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSave(formData);\n  };\n\n  return (\n    <div className=\"role-details\">\n      <h3>{role.name} Role</h3>\n      \n      {isEditing ? (\n        <form onSubmit={handleSubmit}>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Role Name</label>\n              <input\n                type=\"text\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleChange}\n                className=\"form-input\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Role Type</label>\n              <select\n                name=\"type\"\n                value={formData.type}\n                onChange={handleChange}\n                className=\"form-input\"\n              >\n                <option value=\"System Role\">System Role</option>\n                <option value=\"Custom Role\">Custom Role</option>\n                <option value=\"Department Role\">Department Role</option>\n              </select>\n            </div>\n          </div>\n          \n          <div className=\"form-group\">\n            <label className=\"form-label\">Description</label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              className=\"form-input\"\n              rows=\"2\"\n              placeholder=\"Detailed explanation of role purpose\"\n            />\n          </div>\n          \n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Status</label>\n              <select\n                name=\"status\"\n                value={formData.status}\n                onChange={handleChange}\n                className=\"form-input\"\n              >\n                <option value=\"Active\">Active</option>\n                <option value=\"Inactive\">Inactive</option>\n                <option value=\"Deprecated\">Deprecated</option>\n              </select>\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">User Count</label>\n              <input\n                type=\"text\"\n                value={formData.userCount}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Default Role</label>\n              <select\n                name=\"isDefault\"\n                value={formData.isDefault ? 'Yes' : 'No'}\n                onChange={(e) => handleChange({\n                  target: { name: 'isDefault', value: e.target.value === 'Yes', type: 'checkbox', checked: e.target.value === 'Yes' }\n                })}\n                className=\"form-input\"\n              >\n                <option value=\"No\">No</option>\n                <option value=\"Yes\">Yes</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"actions-bar\" style={{ marginTop: '20px' }}>\n            <div>\n              <button type=\"button\" className=\"btn-secondary\" onClick={onCancel}>\n                Cancel\n              </button>\n              <button type=\"submit\" className=\"btn-primary\">\n                Save Changes\n              </button>\n            </div>\n          </div>\n        </form>\n      ) : (\n        <div>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Role Name</label>\n              <input\n                type=\"text\"\n                value={role.name}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Role Type</label>\n              <input\n                type=\"text\"\n                value={role.type}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n          </div>\n          \n          <div className=\"form-group\">\n            <label className=\"form-label\">Description</label>\n            <textarea\n              value={role.description}\n              className=\"form-input\"\n              rows=\"2\"\n              readOnly\n              style={{ background: '#f9fafb' }}\n            />\n          </div>\n          \n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Status</label>\n              <input\n                type=\"text\"\n                value={role.status}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">User Count</label>\n              <input\n                type=\"text\"\n                value={role.userCount}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Default Role</label>\n              <input\n                type=\"text\"\n                value={role.isDefault ? 'Yes' : 'No'}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n          </div>\n          \n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Created Date</label>\n              <input\n                type=\"text\"\n                value={role.createdDate}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Created By</label>\n              <input\n                type=\"text\"\n                value={role.createdBy}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n          </div>\n          \n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Last Modified</label>\n              <input\n                type=\"text\"\n                value={role.lastModified}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label className=\"form-label\">Modified By</label>\n              <input\n                type=\"text\"\n                value={role.modifiedBy}\n                className=\"form-input\"\n                readOnly\n                style={{ background: '#f9fafb' }}\n              />\n            </div>\n          </div>\n\n          <div className=\"actions-bar\" style={{ marginTop: '20px' }}>\n            <div>\n              <button type=\"button\" className=\"btn-secondary\" onClick={onEdit}>\n                Edit Role\n              </button>\n            </div>\n            <button type=\"button\" className=\"btn-danger\" onClick={onDelete}>\n              Delete Role\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default RoleDetails;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS;EAAEC,MAAM;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAACK,IAAI,CAAC;EAE9CJ,SAAS,CAAC,MAAM;IACdY,WAAW,CAACR,IAAI,CAAC;EACnB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMS,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/CP,WAAW,CAACQ,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,YAAY,GAAIP,CAAC,IAAK;IAC1BA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBf,MAAM,CAACI,QAAQ,CAAC;EAClB,CAAC;EAED,oBACET,OAAA;IAAKqB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BtB,OAAA;MAAAsB,QAAA,GAAKpB,IAAI,CAACW,IAAI,EAAC,OAAK;IAAA;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAExBvB,SAAS,gBACRH,OAAA;MAAM2B,QAAQ,EAAER,YAAa;MAAAG,QAAA,gBAC3BtB,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXF,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEL,QAAQ,CAACI,IAAK;YACrBmB,QAAQ,EAAErB,YAAa;YACvBU,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C1B,OAAA;YACEa,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEL,QAAQ,CAACM,IAAK;YACrBiB,QAAQ,EAAErB,YAAa;YACvBU,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBtB,OAAA;cAAQc,KAAK,EAAC,aAAa;cAAAQ,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChD1B,OAAA;cAAQc,KAAK,EAAC,aAAa;cAAAQ,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChD1B,OAAA;cAAQc,KAAK,EAAC,iBAAiB;cAAAQ,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtB,OAAA;UAAOqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjD1B,OAAA;UACEa,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEL,QAAQ,CAACwB,WAAY;UAC5BD,QAAQ,EAAErB,YAAa;UACvBU,SAAS,EAAC,YAAY;UACtBa,IAAI,EAAC,GAAG;UACRC,WAAW,EAAC;QAAsC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1B,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,aAAa;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC/EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C1B,OAAA;YACEa,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAEL,QAAQ,CAAC2B,MAAO;YACvBJ,QAAQ,EAAErB,YAAa;YACvBU,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBtB,OAAA;cAAQc,KAAK,EAAC,QAAQ;cAAAQ,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC1B,OAAA;cAAQc,KAAK,EAAC,UAAU;cAAAQ,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C1B,OAAA;cAAQc,KAAK,EAAC,YAAY;cAAAQ,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEL,QAAQ,CAAC4B,SAAU;YAC1BhB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD1B,OAAA;YACEa,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAEL,QAAQ,CAAC+B,SAAS,GAAG,KAAK,GAAG,IAAK;YACzCR,QAAQ,EAAGpB,CAAC,IAAKD,YAAY,CAAC;cAC5BM,MAAM,EAAE;gBAAEJ,IAAI,EAAE,WAAW;gBAAEC,KAAK,EAAEF,CAAC,CAACK,MAAM,CAACH,KAAK,KAAK,KAAK;gBAAEC,IAAI,EAAE,UAAU;gBAAEC,OAAO,EAAEJ,CAAC,CAACK,MAAM,CAACH,KAAK,KAAK;cAAM;YACpH,CAAC,CAAE;YACHO,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBtB,OAAA;cAAQc,KAAK,EAAC,IAAI;cAAAQ,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9B1B,OAAA;cAAQc,KAAK,EAAC,KAAK;cAAAQ,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAACO,KAAK,EAAE;UAAEa,SAAS,EAAE;QAAO,CAAE;QAAAnB,QAAA,eACxDtB,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAQe,IAAI,EAAC,QAAQ;YAACM,SAAS,EAAC,eAAe;YAACqB,OAAO,EAAEpC,QAAS;YAAAgB,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1B,OAAA;YAAQe,IAAI,EAAC,QAAQ;YAACM,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,gBAEP1B,OAAA;MAAAsB,QAAA,gBACEtB,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACW,IAAK;YACjBQ,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACa,IAAK;YACjBM,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtB,OAAA;UAAOqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjD1B,OAAA;UACEc,KAAK,EAAEZ,IAAI,CAAC+B,WAAY;UACxBZ,SAAS,EAAC,YAAY;UACtBa,IAAI,EAAC,GAAG;UACRI,QAAQ;UACRV,KAAK,EAAE;YAAEW,UAAU,EAAE;UAAU;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1B,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,aAAa;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC/EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACkC,MAAO;YACnBf,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACmC,SAAU;YACtBhB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACsC,SAAS,GAAG,KAAK,GAAG,IAAK;YACrCnB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAACyC,WAAY;YACxBtB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAAC0C,SAAU;YACtBvB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAK4B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,SAAS;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3EtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAAC2C,YAAa;YACzBxB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtB,OAAA;YAAOqB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD1B,OAAA;YACEe,IAAI,EAAC,MAAM;YACXD,KAAK,EAAEZ,IAAI,CAAC4C,UAAW;YACvBzB,SAAS,EAAC,YAAY;YACtBiB,QAAQ;YACRV,KAAK,EAAE;cAAEW,UAAU,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAACO,KAAK,EAAE;UAAEa,SAAS,EAAE;QAAO,CAAE;QAAAnB,QAAA,gBACxDtB,OAAA;UAAAsB,QAAA,eACEtB,OAAA;YAAQe,IAAI,EAAC,QAAQ;YAACM,SAAS,EAAC,eAAe;YAACqB,OAAO,EAAEtC,MAAO;YAAAkB,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1B,OAAA;UAAQe,IAAI,EAAC,QAAQ;UAACM,SAAS,EAAC,YAAY;UAACqB,OAAO,EAAEnC,QAAS;UAAAe,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CApPIP,WAAW;AAAA8C,EAAA,GAAX9C,WAAW;AAsPjB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}