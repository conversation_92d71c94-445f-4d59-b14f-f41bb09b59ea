{"ast": null, "code": "/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"8\",\n  key: \"1gshiw\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}], [\"path\", {\n  d: \"M7 22h10\",\n  key: \"10w4w3\"\n}], [\"path\", {\n  d: \"M12 22v-4\",\n  key: \"1utk9m\"\n}]];\nconst Webcam = createLucideIcon(\"webcam\", __iconNode);\nexport { __iconNode, Webcam as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Webcam", "createLucideIcon"], "sources": ["D:\\R-NeuroIQ-Admin-Portal\\node_modules\\lucide-react\\src\\icons\\webcam.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '10', r: '8', key: '1gshiw' }],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n  ['path', { d: 'M7 22h10', key: '10w4w3' }],\n  ['path', { d: 'M12 22v-4', key: '1utk9m' }],\n];\n\n/**\n * @component @name Webcam\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSI4IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTcgMjJoMTAiIC8+CiAgPHBhdGggZD0iTTEyIDIydi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/webcam\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Webcam = createLucideIcon('webcam', __iconNode);\n\nexport default Webcam;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAE,MAAA,GAASC,gBAAiB,WAAUP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}