{"ast": null, "code": "export const PERMISSION_CATEGORIES = {\n  USER_MANAGEMENT: {\n    id: 'user_management',\n    title: 'User Management',\n    description: 'Control user accounts and profiles',\n    permissions: [{\n      id: 'create_users',\n      name: 'Create Users',\n      description: 'Add new users to the system'\n    }, {\n      id: 'view_user_profiles',\n      name: 'View User Profiles',\n      description: 'Access user profiles and information'\n    }, {\n      id: 'edit_user_information',\n      name: 'Edit User Information',\n      description: 'Modify user profiles and data'\n    }, {\n      id: 'delete_deactivate_users',\n      name: 'Delete/Deactivate Users',\n      description: 'Remove or disable user accounts'\n    }, {\n      id: 'assign_remove_roles',\n      name: 'Assign/Remove Roles',\n      description: 'Manage user role assignments'\n    }, {\n      id: 'reset_user_passwords',\n      name: 'Reset User Passwords',\n      description: 'Reset passwords for user accounts'\n    }, {\n      id: 'view_user_activity_logs',\n      name: 'View User Activity Logs',\n      description: 'Access user activity and audit trails'\n    }]\n  },\n  SYSTEM_ADMINISTRATION: {\n    id: 'system_administration',\n    title: 'System Administration',\n    description: 'Configure system-wide settings and administration',\n    permissions: [{\n      id: 'access_admin_dashboard',\n      name: 'Access Admin Dashboard',\n      description: 'View administrative interface'\n    }, {\n      id: 'modify_system_settings',\n      name: 'Modify System Settings',\n      description: 'Change system configuration'\n    }, {\n      id: 'manage_integrations',\n      name: 'Manage Integrations',\n      description: 'Configure third-party integrations'\n    }, {\n      id: 'view_system_logs',\n      name: 'View System Logs',\n      description: 'Access system activity logs'\n    }, {\n      id: 'backup_restore_data',\n      name: 'Backup/Restore Data',\n      description: 'Manage system backups and restoration'\n    }, {\n      id: 'manage_api_keys',\n      name: 'Manage API Keys',\n      description: 'Create and manage API access keys'\n    }]\n  },\n  DATA_REPORTS: {\n    id: 'data_reports',\n    title: 'Data & Reports',\n    description: 'Access reports and data analytics',\n    permissions: [{\n      id: 'view_basic_reports',\n      name: 'View Basic Reports',\n      description: 'Access standard system reports'\n    }, {\n      id: 'view_advanced_analytics',\n      name: 'View Advanced Analytics',\n      description: 'Access detailed analytics and insights'\n    }, {\n      id: 'export_data',\n      name: 'Export Data',\n      description: 'Download data in various formats'\n    }, {\n      id: 'create_custom_reports',\n      name: 'Create Custom Reports',\n      description: 'Build and save custom report templates'\n    }, {\n      id: 'view_audit_logs',\n      name: 'View Audit Logs',\n      description: 'Access system audit and compliance logs'\n    }]\n  },\n  COMMUNICATION: {\n    id: 'communication',\n    title: 'Communication',\n    description: 'Manage notifications and communication tools',\n    permissions: [{\n      id: 'send_notifications',\n      name: 'Send Notifications',\n      description: 'Send system notifications to users'\n    }, {\n      id: 'broadcast_messages',\n      name: 'Broadcast Messages',\n      description: 'Send organization-wide messages'\n    }, {\n      id: 'manage_email_templates',\n      name: 'Manage Email Templates',\n      description: 'Create and modify email templates'\n    }, {\n      id: 'manage_announcements',\n      name: 'Manage Announcements',\n      description: 'Create and publish announcements'\n    }]\n  }\n};\nexport const DEFAULT_ROLES = [{\n  id: 1,\n  name: 'Administrator',\n  type: 'System Role',\n  description: 'Full system access with all permissions for managing users and system settings',\n  status: 'Active',\n  userCount: 12,\n  isDefault: false,\n  createdDate: '2024-01-15',\n  createdBy: 'System Admin',\n  lastModified: '2025-08-12',\n  modifiedBy: 'John Doe',\n  permissions: ['create_users', 'view_user_profiles', 'edit_user_information', 'delete_deactivate_users', 'assign_remove_roles', 'reset_user_passwords', 'view_user_activity_logs', 'access_admin_dashboard', 'modify_system_settings', 'manage_integrations', 'view_system_logs', 'backup_restore_data', 'manage_api_keys', 'view_basic_reports', 'view_advanced_analytics', 'export_data', 'create_custom_reports', 'view_audit_logs', 'send_notifications', 'broadcast_messages', 'manage_email_templates', 'manage_announcements']\n}, {\n  id: 2,\n  name: 'Manager',\n  type: 'Custom Role',\n  description: 'Team management access with user and report permissions',\n  status: 'Active',\n  userCount: 8,\n  isDefault: false,\n  createdDate: '2024-02-10',\n  createdBy: 'John Doe',\n  lastModified: '2025-07-20',\n  modifiedBy: 'Jane Smith',\n  permissions: ['view_user_profiles', 'view_basic_reports', 'send_notifications']\n}, {\n  id: 3,\n  name: 'Editor',\n  type: 'Department Role',\n  description: 'Content creation and editing capabilities',\n  status: 'Active',\n  userCount: 24,\n  isDefault: false,\n  createdDate: '2024-01-20',\n  createdBy: 'John Doe',\n  lastModified: '2025-06-15',\n  modifiedBy: 'Bob Wilson',\n  permissions: ['view_basic_reports', 'send_notifications']\n}, {\n  id: 4,\n  name: 'Viewer',\n  type: 'Custom Role',\n  description: 'Read-only access to basic system features',\n  status: 'Active',\n  userCount: 45,\n  isDefault: true,\n  createdDate: '2024-01-15',\n  createdBy: 'System Admin',\n  lastModified: '2025-03-10',\n  modifiedBy: 'Jane Smith',\n  permissions: ['view_basic_reports']\n}];", "map": {"version": 3, "names": ["PERMISSION_CATEGORIES", "USER_MANAGEMENT", "id", "title", "description", "permissions", "name", "SYSTEM_ADMINISTRATION", "DATA_REPORTS", "COMMUNICATION", "DEFAULT_ROLES", "type", "status", "userCount", "isDefault", "createdDate", "created<PERSON>y", "lastModified", "modifiedBy"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/data/permissionsData.js"], "sourcesContent": ["export const PERMISSION_CATEGORIES = {\n  USER_MANAGEMENT: {\n    id: 'user_management',\n    title: 'User Management',\n    description: 'Control user accounts and profiles',\n    permissions: [\n      {\n        id: 'create_users',\n        name: 'Create Users',\n        description: 'Add new users to the system'\n      },\n      {\n        id: 'view_user_profiles',\n        name: 'View User Profiles',\n        description: 'Access user profiles and information'\n      },\n      {\n        id: 'edit_user_information',\n        name: 'Edit User Information',\n        description: 'Modify user profiles and data'\n      },\n      {\n        id: 'delete_deactivate_users',\n        name: 'Delete/Deactivate Users',\n        description: 'Remove or disable user accounts'\n      },\n      {\n        id: 'assign_remove_roles',\n        name: 'Assign/Remove Roles',\n        description: 'Manage user role assignments'\n      },\n      {\n        id: 'reset_user_passwords',\n        name: 'Reset User Passwords',\n        description: 'Reset passwords for user accounts'\n      },\n      {\n        id: 'view_user_activity_logs',\n        name: 'View User Activity Logs',\n        description: 'Access user activity and audit trails'\n      }\n    ]\n  },\n  SYSTEM_ADMINISTRATION: {\n    id: 'system_administration',\n    title: 'System Administration',\n    description: 'Configure system-wide settings and administration',\n    permissions: [\n      {\n        id: 'access_admin_dashboard',\n        name: 'Access Admin Dashboard',\n        description: 'View administrative interface'\n      },\n      {\n        id: 'modify_system_settings',\n        name: 'Modify System Settings',\n        description: 'Change system configuration'\n      },\n      {\n        id: 'manage_integrations',\n        name: 'Manage Integrations',\n        description: 'Configure third-party integrations'\n      },\n      {\n        id: 'view_system_logs',\n        name: 'View System Logs',\n        description: 'Access system activity logs'\n      },\n      {\n        id: 'backup_restore_data',\n        name: 'Backup/Restore Data',\n        description: 'Manage system backups and restoration'\n      },\n      {\n        id: 'manage_api_keys',\n        name: 'Manage API Keys',\n        description: 'Create and manage API access keys'\n      }\n    ]\n  },\n  DATA_REPORTS: {\n    id: 'data_reports',\n    title: 'Data & Reports',\n    description: 'Access reports and data analytics',\n    permissions: [\n      {\n        id: 'view_basic_reports',\n        name: 'View Basic Reports',\n        description: 'Access standard system reports'\n      },\n      {\n        id: 'view_advanced_analytics',\n        name: 'View Advanced Analytics',\n        description: 'Access detailed analytics and insights'\n      },\n      {\n        id: 'export_data',\n        name: 'Export Data',\n        description: 'Download data in various formats'\n      },\n      {\n        id: 'create_custom_reports',\n        name: 'Create Custom Reports',\n        description: 'Build and save custom report templates'\n      },\n      {\n        id: 'view_audit_logs',\n        name: 'View Audit Logs',\n        description: 'Access system audit and compliance logs'\n      }\n    ]\n  },\n  COMMUNICATION: {\n    id: 'communication',\n    title: 'Communication',\n    description: 'Manage notifications and communication tools',\n    permissions: [\n      {\n        id: 'send_notifications',\n        name: 'Send Notifications',\n        description: 'Send system notifications to users'\n      },\n      {\n        id: 'broadcast_messages',\n        name: 'Broadcast Messages',\n        description: 'Send organization-wide messages'\n      },\n      {\n        id: 'manage_email_templates',\n        name: 'Manage Email Templates',\n        description: 'Create and modify email templates'\n      },\n      {\n        id: 'manage_announcements',\n        name: 'Manage Announcements',\n        description: 'Create and publish announcements'\n      }\n    ]\n  }\n};\n\nexport const DEFAULT_ROLES = [\n  {\n    id: 1,\n    name: 'Administrator',\n    type: 'System Role',\n    description: 'Full system access with all permissions for managing users and system settings',\n    status: 'Active',\n    userCount: 12,\n    isDefault: false,\n    createdDate: '2024-01-15',\n    createdBy: 'System Admin',\n    lastModified: '2025-08-12',\n    modifiedBy: 'John Doe',\n    permissions: [\n      'create_users', 'view_user_profiles', 'edit_user_information', 'delete_deactivate_users',\n      'assign_remove_roles', 'reset_user_passwords', 'view_user_activity_logs',\n      'access_admin_dashboard', 'modify_system_settings', 'manage_integrations',\n      'view_system_logs', 'backup_restore_data', 'manage_api_keys',\n      'view_basic_reports', 'view_advanced_analytics', 'export_data',\n      'create_custom_reports', 'view_audit_logs',\n      'send_notifications', 'broadcast_messages', 'manage_email_templates', 'manage_announcements'\n    ]\n  },\n  {\n    id: 2,\n    name: 'Manager',\n    type: 'Custom Role',\n    description: 'Team management access with user and report permissions',\n    status: 'Active',\n    userCount: 8,\n    isDefault: false,\n    createdDate: '2024-02-10',\n    createdBy: 'John Doe',\n    lastModified: '2025-07-20',\n    modifiedBy: 'Jane Smith',\n    permissions: ['view_user_profiles', 'view_basic_reports', 'send_notifications']\n  },\n  {\n    id: 3,\n    name: 'Editor',\n    type: 'Department Role',\n    description: 'Content creation and editing capabilities',\n    status: 'Active',\n    userCount: 24,\n    isDefault: false,\n    createdDate: '2024-01-20',\n    createdBy: 'John Doe',\n    lastModified: '2025-06-15',\n    modifiedBy: 'Bob Wilson',\n    permissions: ['view_basic_reports', 'send_notifications']\n  },\n  {\n    id: 4,\n    name: 'Viewer',\n    type: 'Custom Role',\n    description: 'Read-only access to basic system features',\n    status: 'Active',\n    userCount: 45,\n    isDefault: true,\n    createdDate: '2024-01-15',\n    createdBy: 'System Admin',\n    lastModified: '2025-03-10',\n    modifiedBy: 'Jane Smith',\n    permissions: ['view_basic_reports']\n  }\n];"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG;EACnCC,eAAe,EAAE;IACfC,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,oCAAoC;IACjDC,WAAW,EAAE,CACX;MACEH,EAAE,EAAE,cAAc;MAClBI,IAAI,EAAE,cAAc;MACpBF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,oBAAoB;MACxBI,IAAI,EAAE,oBAAoB;MAC1BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,uBAAuB;MAC3BI,IAAI,EAAE,uBAAuB;MAC7BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,yBAAyB;MAC7BI,IAAI,EAAE,yBAAyB;MAC/BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,qBAAqB;MACzBI,IAAI,EAAE,qBAAqB;MAC3BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,sBAAsB;MAC1BI,IAAI,EAAE,sBAAsB;MAC5BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,yBAAyB;MAC7BI,IAAI,EAAE,yBAAyB;MAC/BF,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EACDG,qBAAqB,EAAE;IACrBL,EAAE,EAAE,uBAAuB;IAC3BC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,mDAAmD;IAChEC,WAAW,EAAE,CACX;MACEH,EAAE,EAAE,wBAAwB;MAC5BI,IAAI,EAAE,wBAAwB;MAC9BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,wBAAwB;MAC5BI,IAAI,EAAE,wBAAwB;MAC9BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,qBAAqB;MACzBI,IAAI,EAAE,qBAAqB;MAC3BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,kBAAkB;MACtBI,IAAI,EAAE,kBAAkB;MACxBF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,qBAAqB;MACzBI,IAAI,EAAE,qBAAqB;MAC3BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,iBAAiB;MACrBI,IAAI,EAAE,iBAAiB;MACvBF,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EACDI,YAAY,EAAE;IACZN,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,mCAAmC;IAChDC,WAAW,EAAE,CACX;MACEH,EAAE,EAAE,oBAAoB;MACxBI,IAAI,EAAE,oBAAoB;MAC1BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,yBAAyB;MAC7BI,IAAI,EAAE,yBAAyB;MAC/BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,aAAa;MACjBI,IAAI,EAAE,aAAa;MACnBF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,uBAAuB;MAC3BI,IAAI,EAAE,uBAAuB;MAC7BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,iBAAiB;MACrBI,IAAI,EAAE,iBAAiB;MACvBF,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EACDK,aAAa,EAAE;IACbP,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,8CAA8C;IAC3DC,WAAW,EAAE,CACX;MACEH,EAAE,EAAE,oBAAoB;MACxBI,IAAI,EAAE,oBAAoB;MAC1BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,oBAAoB;MACxBI,IAAI,EAAE,oBAAoB;MAC1BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,wBAAwB;MAC5BI,IAAI,EAAE,wBAAwB;MAC9BF,WAAW,EAAE;IACf,CAAC,EACD;MACEF,EAAE,EAAE,sBAAsB;MAC1BI,IAAI,EAAE,sBAAsB;MAC5BF,WAAW,EAAE;IACf,CAAC;EAEL;AACF,CAAC;AAED,OAAO,MAAMM,aAAa,GAAG,CAC3B;EACER,EAAE,EAAE,CAAC;EACLI,IAAI,EAAE,eAAe;EACrBK,IAAI,EAAE,aAAa;EACnBP,WAAW,EAAE,gFAAgF;EAC7FQ,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,YAAY;EACzBC,SAAS,EAAE,cAAc;EACzBC,YAAY,EAAE,YAAY;EAC1BC,UAAU,EAAE,UAAU;EACtBb,WAAW,EAAE,CACX,cAAc,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,yBAAyB,EACxF,qBAAqB,EAAE,sBAAsB,EAAE,yBAAyB,EACxE,wBAAwB,EAAE,wBAAwB,EAAE,qBAAqB,EACzE,kBAAkB,EAAE,qBAAqB,EAAE,iBAAiB,EAC5D,oBAAoB,EAAE,yBAAyB,EAAE,aAAa,EAC9D,uBAAuB,EAAE,iBAAiB,EAC1C,oBAAoB,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,sBAAsB;AAEhG,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLI,IAAI,EAAE,SAAS;EACfK,IAAI,EAAE,aAAa;EACnBP,WAAW,EAAE,yDAAyD;EACtEQ,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,YAAY;EACzBC,SAAS,EAAE,UAAU;EACrBC,YAAY,EAAE,YAAY;EAC1BC,UAAU,EAAE,YAAY;EACxBb,WAAW,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAChF,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLI,IAAI,EAAE,QAAQ;EACdK,IAAI,EAAE,iBAAiB;EACvBP,WAAW,EAAE,2CAA2C;EACxDQ,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,YAAY;EACzBC,SAAS,EAAE,UAAU;EACrBC,YAAY,EAAE,YAAY;EAC1BC,UAAU,EAAE,YAAY;EACxBb,WAAW,EAAE,CAAC,oBAAoB,EAAE,oBAAoB;AAC1D,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLI,IAAI,EAAE,QAAQ;EACdK,IAAI,EAAE,aAAa;EACnBP,WAAW,EAAE,2CAA2C;EACxDQ,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE,YAAY;EACzBC,SAAS,EAAE,cAAc;EACzBC,YAAY,EAAE,YAAY;EAC1BC,UAAU,EAAE,YAAY;EACxBb,WAAW,EAAE,CAAC,oBAAoB;AACpC,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}