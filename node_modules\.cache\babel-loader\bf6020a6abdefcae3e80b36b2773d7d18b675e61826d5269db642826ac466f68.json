{"ast": null, "code": "var _jsxFileName = \"D:\\\\R-NeuroIQ-Admin-Portal\\\\src\\\\components\\\\accounts\\\\user-roles-permissions\\\\components\\\\PermissionsGrid.jsx\";\nimport React from 'react';\nimport { PERMISSION_CATEGORIES } from '../../../../data/permissionsData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PermissionsGrid = ({\n  permissions,\n  onTogglePermission,\n  onSave,\n  loading\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"permissions-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"panel-header\",\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"panel-title\",\n        children: \"Permissions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: onSave,\n        disabled: loading,\n        children: loading ? 'Saving...' : 'Save Permissions'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"permissions-grid\",\n      children: Object.values(PERMISSION_CATEGORIES).map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"permission-category\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"category-title\",\n            children: category.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"category-description\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"permissions-list\",\n          children: category.permissions.map(permission => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"permission-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"permission-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: permission.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: permission.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `permission-toggle ${permissions[permission.id] ? 'active' : ''}`,\n              onClick: () => onTogglePermission(permission.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 19\n            }, this)]\n          }, permission.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = PermissionsGrid;\nexport default PermissionsGrid;\nvar _c;\n$RefreshReg$(_c, \"PermissionsGrid\");", "map": {"version": 3, "names": ["React", "PERMISSION_CATEGORIES", "jsxDEV", "_jsxDEV", "PermissionsGrid", "permissions", "onTogglePermission", "onSave", "loading", "className", "children", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "Object", "values", "map", "category", "title", "description", "permission", "name", "id", "_c", "$RefreshReg$"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/components/accounts/user-roles-permissions/components/PermissionsGrid.jsx"], "sourcesContent": ["import React from 'react';\nimport { PERMISSION_CATEGORIES } from '../../../../data/permissionsData';\n\nconst PermissionsGrid = ({ permissions, onTogglePermission, onSave, loading }) => {\n  return (\n    <div className=\"permissions-section\">\n      <div className=\"panel-header\" style={{ marginBottom: '20px' }}>\n        <h3 className=\"panel-title\">Permissions</h3>\n        <button\n          className=\"btn-primary\"\n          onClick={onSave}\n          disabled={loading}\n        >\n          {loading ? 'Saving...' : 'Save Permissions'}\n        </button>\n      </div>\n\n      <div className=\"permissions-grid\">\n        {Object.values(PERMISSION_CATEGORIES).map((category) => (\n          <div key={category.id} className=\"permission-category\">\n            <div className=\"category-header\">\n              <h4 className=\"category-title\">{category.title}</h4>\n              <p className=\"category-description\">{category.description}</p>\n            </div>\n            \n            <div className=\"permissions-list\">\n              {category.permissions.map((permission) => (\n                <div key={permission.id} className=\"permission-item\">\n                  <div className=\"permission-info\">\n                    <h5>{permission.name}</h5>\n                    <p>{permission.description}</p>\n                  </div>\n                  <div\n                    className={`permission-toggle ${permissions[permission.id] ? 'active' : ''}`}\n                    onClick={() => onTogglePermission(permission.id)}\n                  />\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default PermissionsGrid;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,eAAe,GAAGA,CAAC;EAAEC,WAAW;EAAEC,kBAAkB;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAChF,oBACEL,OAAA;IAAKM,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCP,OAAA;MAAKM,SAAS,EAAC,cAAc;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC5DP,OAAA;QAAIM,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5Cb,OAAA;QACEM,SAAS,EAAC,aAAa;QACvBQ,OAAO,EAAEV,MAAO;QAChBW,QAAQ,EAAEV,OAAQ;QAAAE,QAAA,EAEjBF,OAAO,GAAG,WAAW,GAAG;MAAkB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENb,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BS,MAAM,CAACC,MAAM,CAACnB,qBAAqB,CAAC,CAACoB,GAAG,CAAEC,QAAQ,iBACjDnB,OAAA;QAAuBM,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACpDP,OAAA;UAAKM,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BP,OAAA;YAAIM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEY,QAAQ,CAACC;UAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDb,OAAA;YAAGM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEY,QAAQ,CAACE;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENb,OAAA;UAAKM,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BY,QAAQ,CAACjB,WAAW,CAACgB,GAAG,CAAEI,UAAU,iBACnCtB,OAAA;YAAyBM,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAClDP,OAAA;cAAKM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BP,OAAA;gBAAAO,QAAA,EAAKe,UAAU,CAACC;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1Bb,OAAA;gBAAAO,QAAA,EAAIe,UAAU,CAACD;cAAW;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNb,OAAA;cACEM,SAAS,EAAE,qBAAqBJ,WAAW,CAACoB,UAAU,CAACE,EAAE,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7EV,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACmB,UAAU,CAACE,EAAE;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA,GARMS,UAAU,CAACE,EAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASlB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAnBEM,QAAQ,CAACK,EAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GAzCIxB,eAAe;AA2CrB,eAAeA,eAAe;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}