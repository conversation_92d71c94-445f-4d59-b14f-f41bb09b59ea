{"ast": null, "code": "// RBAC Service\n// Handles role-based access control operations\n\nimport apiService, { ApiError } from './api';\nclass RBACService {\n  // Role Management\n  async getAllRoles(includeInactive = false) {\n    try {\n      const params = includeInactive ? {\n        include_inactive: true\n      } : {};\n      const response = await apiService.get('/api/rbac/roles', params);\n      return {\n        success: true,\n        roles: response\n      };\n    } catch (error) {\n      console.error('Get roles error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch roles'\n      };\n    }\n  }\n  async getRoleById(roleId) {\n    try {\n      const response = await apiService.get(`/api/rbac/roles/${roleId}`);\n      return {\n        success: true,\n        role: response\n      };\n    } catch (error) {\n      console.error('Get role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch role'\n      };\n    }\n  }\n  async createRole(roleData) {\n    try {\n      // Transform frontend role data to backend format\n      const backendRoleData = {\n        role_name: roleData.roleName || roleData.role_name,\n        role_description: roleData.description || roleData.role_description,\n        permissions: roleData.permissions || {}\n      };\n      const response = await apiService.post('/api/rbac/roles', backendRoleData);\n      return {\n        success: true,\n        role: response,\n        message: 'Role created successfully'\n      };\n    } catch (error) {\n      console.error('Create role error:', error);\n      let errorMessage = 'Failed to create role';\n      if (error instanceof ApiError) {\n        if (error.status === 400) {\n          var _error$data;\n          errorMessage = ((_error$data = error.data) === null || _error$data === void 0 ? void 0 : _error$data.detail) || 'Role with this name already exists';\n        } else if (error.status === 403) {\n          errorMessage = 'Insufficient permissions to create roles';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  }\n  async updateRole(roleId, roleData) {\n    try {\n      const backendRoleData = {\n        role_name: roleData.roleName || roleData.role_name,\n        role_description: roleData.description || roleData.role_description,\n        permissions: roleData.permissions || {}\n      };\n      const response = await apiService.put(`/api/rbac/roles/${roleId}`, backendRoleData);\n      return {\n        success: true,\n        role: response,\n        message: 'Role updated successfully'\n      };\n    } catch (error) {\n      console.error('Update role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to update role'\n      };\n    }\n  }\n  async deleteRole(roleId) {\n    try {\n      await apiService.delete(`/api/rbac/roles/${roleId}`);\n      return {\n        success: true,\n        message: 'Role deleted successfully'\n      };\n    } catch (error) {\n      console.error('Delete role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to delete role'\n      };\n    }\n  }\n\n  // Role Assignment\n  async assignRoleToUser(userId, roleId, expiresAt = null) {\n    try {\n      const data = expiresAt ? {\n        expires_at: expiresAt\n      } : {};\n      const response = await apiService.post(`/api/rbac/users/${userId}/roles/${roleId}`, data);\n      return {\n        success: true,\n        assignment: response,\n        message: 'Role assigned successfully'\n      };\n    } catch (error) {\n      console.error('Assign role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to assign role'\n      };\n    }\n  }\n  async revokeRoleFromUser(userId, roleId) {\n    try {\n      await apiService.delete(`/api/rbac/users/${userId}/roles/${roleId}`);\n      return {\n        success: true,\n        message: 'Role revoked successfully'\n      };\n    } catch (error) {\n      console.error('Revoke role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to revoke role'\n      };\n    }\n  }\n  async getUserRoles(userId) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/roles`);\n      return {\n        success: true,\n        roles: response\n      };\n    } catch (error) {\n      console.error('Get user roles error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch user roles'\n      };\n    }\n  }\n  async getRoleUsers(roleId) {\n    try {\n      const response = await apiService.get(`/api/rbac/roles/${roleId}/users`);\n      return {\n        success: true,\n        users: response\n      };\n    } catch (error) {\n      console.error('Get role users error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch role users'\n      };\n    }\n  }\n\n  // Permission Management\n  async getUserPermissions(userId) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/permissions`);\n      return {\n        success: true,\n        permissions: response.permissions\n      };\n    } catch (error) {\n      console.error('Get user permissions error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch user permissions'\n      };\n    }\n  }\n  async checkUserPermission(userId, permission) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/permissions/${permission}`);\n      return {\n        success: true,\n        hasPermission: response.has_permission\n      };\n    } catch (error) {\n      console.error('Check permission error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to check permission'\n      };\n    }\n  }\n}\n\n// Create singleton instance\nconst rbacService = new RBACService();\nexport default rbacService;", "map": {"version": 3, "names": ["apiService", "ApiError", "RBACService", "getAllRoles", "includeInactive", "params", "include_inactive", "response", "get", "success", "roles", "error", "console", "message", "getRoleById", "roleId", "role", "createRole", "roleData", "backendRoleData", "role_name", "<PERSON><PERSON><PERSON>", "role_description", "description", "permissions", "post", "errorMessage", "status", "_error$data", "data", "detail", "updateRole", "put", "deleteRole", "delete", "assignRoleToUser", "userId", "expiresAt", "expires_at", "assignment", "revokeRoleFromUser", "getUserRoles", "getRoleUsers", "users", "getUserPermissions", "checkUserPermission", "permission", "hasPermission", "has_permission", "rbacService"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/services/rbacService.js"], "sourcesContent": ["// RBAC Service\n// Handles role-based access control operations\n\nimport apiService, { ApiError } from './api';\n\nclass RBACService {\n  // Role Management\n  async getAllRoles(includeInactive = false) {\n    try {\n      const params = includeInactive ? { include_inactive: true } : {};\n      const response = await apiService.get('/api/rbac/roles', params);\n      return {\n        success: true,\n        roles: response\n      };\n    } catch (error) {\n      console.error('Get roles error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch roles'\n      };\n    }\n  }\n\n  async getRoleById(roleId) {\n    try {\n      const response = await apiService.get(`/api/rbac/roles/${roleId}`);\n      return {\n        success: true,\n        role: response\n      };\n    } catch (error) {\n      console.error('Get role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch role'\n      };\n    }\n  }\n\n  async createRole(roleData) {\n    try {\n      // Transform frontend role data to backend format\n      const backendRoleData = {\n        role_name: roleData.roleName || roleData.role_name,\n        role_description: roleData.description || roleData.role_description,\n        permissions: roleData.permissions || {}\n      };\n\n      const response = await apiService.post('/api/rbac/roles', backendRoleData);\n      return {\n        success: true,\n        role: response,\n        message: 'Role created successfully'\n      };\n    } catch (error) {\n      console.error('Create role error:', error);\n      \n      let errorMessage = 'Failed to create role';\n      if (error instanceof ApiError) {\n        if (error.status === 400) {\n          errorMessage = error.data?.detail || 'Role with this name already exists';\n        } else if (error.status === 403) {\n          errorMessage = 'Insufficient permissions to create roles';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n      \n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  }\n\n  async updateRole(roleId, roleData) {\n    try {\n      const backendRoleData = {\n        role_name: roleData.roleName || roleData.role_name,\n        role_description: roleData.description || roleData.role_description,\n        permissions: roleData.permissions || {}\n      };\n\n      const response = await apiService.put(`/api/rbac/roles/${roleId}`, backendRoleData);\n      return {\n        success: true,\n        role: response,\n        message: 'Role updated successfully'\n      };\n    } catch (error) {\n      console.error('Update role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to update role'\n      };\n    }\n  }\n\n  async deleteRole(roleId) {\n    try {\n      await apiService.delete(`/api/rbac/roles/${roleId}`);\n      return {\n        success: true,\n        message: 'Role deleted successfully'\n      };\n    } catch (error) {\n      console.error('Delete role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to delete role'\n      };\n    }\n  }\n\n  // Role Assignment\n  async assignRoleToUser(userId, roleId, expiresAt = null) {\n    try {\n      const data = expiresAt ? { expires_at: expiresAt } : {};\n      const response = await apiService.post(`/api/rbac/users/${userId}/roles/${roleId}`, data);\n      return {\n        success: true,\n        assignment: response,\n        message: 'Role assigned successfully'\n      };\n    } catch (error) {\n      console.error('Assign role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to assign role'\n      };\n    }\n  }\n\n  async revokeRoleFromUser(userId, roleId) {\n    try {\n      await apiService.delete(`/api/rbac/users/${userId}/roles/${roleId}`);\n      return {\n        success: true,\n        message: 'Role revoked successfully'\n      };\n    } catch (error) {\n      console.error('Revoke role error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to revoke role'\n      };\n    }\n  }\n\n  async getUserRoles(userId) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/roles`);\n      return {\n        success: true,\n        roles: response\n      };\n    } catch (error) {\n      console.error('Get user roles error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch user roles'\n      };\n    }\n  }\n\n  async getRoleUsers(roleId) {\n    try {\n      const response = await apiService.get(`/api/rbac/roles/${roleId}/users`);\n      return {\n        success: true,\n        users: response\n      };\n    } catch (error) {\n      console.error('Get role users error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch role users'\n      };\n    }\n  }\n\n  // Permission Management\n  async getUserPermissions(userId) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/permissions`);\n      return {\n        success: true,\n        permissions: response.permissions\n      };\n    } catch (error) {\n      console.error('Get user permissions error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to fetch user permissions'\n      };\n    }\n  }\n\n  async checkUserPermission(userId, permission) {\n    try {\n      const response = await apiService.get(`/api/rbac/users/${userId}/permissions/${permission}`);\n      return {\n        success: true,\n        hasPermission: response.has_permission\n      };\n    } catch (error) {\n      console.error('Check permission error:', error);\n      return {\n        success: false,\n        error: error.message || 'Failed to check permission'\n      };\n    }\n  }\n}\n\n// Create singleton instance\nconst rbacService = new RBACService();\n\nexport default rbacService;\n"], "mappings": "AAAA;AACA;;AAEA,OAAOA,UAAU,IAAIC,QAAQ,QAAQ,OAAO;AAE5C,MAAMC,WAAW,CAAC;EAChB;EACA,MAAMC,WAAWA,CAACC,eAAe,GAAG,KAAK,EAAE;IACzC,IAAI;MACF,MAAMC,MAAM,GAAGD,eAAe,GAAG;QAAEE,gBAAgB,EAAE;MAAK,CAAC,GAAG,CAAC,CAAC;MAChE,MAAMC,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,iBAAiB,EAAEH,MAAM,CAAC;MAChE,OAAO;QACLI,OAAO,EAAE,IAAI;QACbC,KAAK,EAAEH;MACT,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAMC,WAAWA,CAACC,MAAM,EAAE;IACxB,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,mBAAmBO,MAAM,EAAE,CAAC;MAClE,OAAO;QACLN,OAAO,EAAE,IAAI;QACbO,IAAI,EAAET;MACR,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAMI,UAAUA,CAACC,QAAQ,EAAE;IACzB,IAAI;MACF;MACA,MAAMC,eAAe,GAAG;QACtBC,SAAS,EAAEF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACE,SAAS;QAClDE,gBAAgB,EAAEJ,QAAQ,CAACK,WAAW,IAAIL,QAAQ,CAACI,gBAAgB;QACnEE,WAAW,EAAEN,QAAQ,CAACM,WAAW,IAAI,CAAC;MACxC,CAAC;MAED,MAAMjB,QAAQ,GAAG,MAAMP,UAAU,CAACyB,IAAI,CAAC,iBAAiB,EAAEN,eAAe,CAAC;MAC1E,OAAO;QACLV,OAAO,EAAE,IAAI;QACbO,IAAI,EAAET,QAAQ;QACdM,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,IAAIe,YAAY,GAAG,uBAAuB;MAC1C,IAAIf,KAAK,YAAYV,QAAQ,EAAE;QAC7B,IAAIU,KAAK,CAACgB,MAAM,KAAK,GAAG,EAAE;UAAA,IAAAC,WAAA;UACxBF,YAAY,GAAG,EAAAE,WAAA,GAAAjB,KAAK,CAACkB,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,MAAM,KAAI,oCAAoC;QAC3E,CAAC,MAAM,IAAInB,KAAK,CAACgB,MAAM,KAAK,GAAG,EAAE;UAC/BD,YAAY,GAAG,0CAA0C;QAC3D,CAAC,MAAM;UACLA,YAAY,GAAGf,KAAK,CAACE,OAAO;QAC9B;MACF;MAEA,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEe;MACT,CAAC;IACH;EACF;EAEA,MAAMK,UAAUA,CAAChB,MAAM,EAAEG,QAAQ,EAAE;IACjC,IAAI;MACF,MAAMC,eAAe,GAAG;QACtBC,SAAS,EAAEF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACE,SAAS;QAClDE,gBAAgB,EAAEJ,QAAQ,CAACK,WAAW,IAAIL,QAAQ,CAACI,gBAAgB;QACnEE,WAAW,EAAEN,QAAQ,CAACM,WAAW,IAAI,CAAC;MACxC,CAAC;MAED,MAAMjB,QAAQ,GAAG,MAAMP,UAAU,CAACgC,GAAG,CAAC,mBAAmBjB,MAAM,EAAE,EAAEI,eAAe,CAAC;MACnF,OAAO;QACLV,OAAO,EAAE,IAAI;QACbO,IAAI,EAAET,QAAQ;QACdM,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAMoB,UAAUA,CAAClB,MAAM,EAAE;IACvB,IAAI;MACF,MAAMf,UAAU,CAACkC,MAAM,CAAC,mBAAmBnB,MAAM,EAAE,CAAC;MACpD,OAAO;QACLN,OAAO,EAAE,IAAI;QACbI,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;;EAEA;EACA,MAAMsB,gBAAgBA,CAACC,MAAM,EAAErB,MAAM,EAAEsB,SAAS,GAAG,IAAI,EAAE;IACvD,IAAI;MACF,MAAMR,IAAI,GAAGQ,SAAS,GAAG;QAAEC,UAAU,EAAED;MAAU,CAAC,GAAG,CAAC,CAAC;MACvD,MAAM9B,QAAQ,GAAG,MAAMP,UAAU,CAACyB,IAAI,CAAC,mBAAmBW,MAAM,UAAUrB,MAAM,EAAE,EAAEc,IAAI,CAAC;MACzF,OAAO;QACLpB,OAAO,EAAE,IAAI;QACb8B,UAAU,EAAEhC,QAAQ;QACpBM,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAM2B,kBAAkBA,CAACJ,MAAM,EAAErB,MAAM,EAAE;IACvC,IAAI;MACF,MAAMf,UAAU,CAACkC,MAAM,CAAC,mBAAmBE,MAAM,UAAUrB,MAAM,EAAE,CAAC;MACpE,OAAO;QACLN,OAAO,EAAE,IAAI;QACbI,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAM4B,YAAYA,CAACL,MAAM,EAAE;IACzB,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,mBAAmB4B,MAAM,QAAQ,CAAC;MACxE,OAAO;QACL3B,OAAO,EAAE,IAAI;QACbC,KAAK,EAAEH;MACT,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAM6B,YAAYA,CAAC3B,MAAM,EAAE;IACzB,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,mBAAmBO,MAAM,QAAQ,CAAC;MACxE,OAAO;QACLN,OAAO,EAAE,IAAI;QACbkC,KAAK,EAAEpC;MACT,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;;EAEA;EACA,MAAM+B,kBAAkBA,CAACR,MAAM,EAAE;IAC/B,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,mBAAmB4B,MAAM,cAAc,CAAC;MAC9E,OAAO;QACL3B,OAAO,EAAE,IAAI;QACbe,WAAW,EAAEjB,QAAQ,CAACiB;MACxB,CAAC;IACH,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;EAEA,MAAMgC,mBAAmBA,CAACT,MAAM,EAAEU,UAAU,EAAE;IAC5C,IAAI;MACF,MAAMvC,QAAQ,GAAG,MAAMP,UAAU,CAACQ,GAAG,CAAC,mBAAmB4B,MAAM,gBAAgBU,UAAU,EAAE,CAAC;MAC5F,OAAO;QACLrC,OAAO,EAAE,IAAI;QACbsC,aAAa,EAAExC,QAAQ,CAACyC;MAC1B,CAAC;IACH,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QACLF,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAC1B,CAAC;IACH;EACF;AACF;;AAEA;AACA,MAAMoC,WAAW,GAAG,IAAI/C,WAAW,CAAC,CAAC;AAErC,eAAe+C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}