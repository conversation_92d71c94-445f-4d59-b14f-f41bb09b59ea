{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PERMISSION_CATEGORIES } from '../data/permissionsData';\nimport rbacService from '../services/rbacService';\nimport { toast } from 'react-toastify';\nexport const usePermissions = selectedRole => {\n  _s();\n  const [permissions, setPermissions] = useState({});\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    if (selectedRole) {\n      initializePermissions();\n    }\n  }, [selectedRole]);\n  const initializePermissions = () => {\n    if (!selectedRole) {\n      setPermissions({});\n      return;\n    }\n    const rolePermissions = selectedRole.permissions || {};\n    const permissionState = {};\n\n    // Handle both array format (frontend) and object format (backend)\n    if (Array.isArray(rolePermissions)) {\n      // Frontend format - array of permission IDs\n      Object.values(PERMISSION_CATEGORIES).forEach(category => {\n        category.permissions.forEach(permission => {\n          permissionState[permission.id] = rolePermissions.includes(permission.id);\n        });\n      });\n    } else {\n      // Backend format - object with permission keys\n      Object.values(PERMISSION_CATEGORIES).forEach(category => {\n        category.permissions.forEach(permission => {\n          permissionState[permission.id] = rolePermissions[permission.id] === true;\n        });\n      });\n    }\n    setPermissions(permissionState);\n  };\n  const togglePermission = permissionId => {\n    setPermissions(prev => ({\n      ...prev,\n      [permissionId]: !prev[permissionId]\n    }));\n  };\n  const getActivePermissions = () => {\n    return Object.entries(permissions).filter(([_, isActive]) => isActive).map(([permissionId]) => permissionId);\n  };\n  const savePermissions = async () => {\n    if (!selectedRole) {\n      toast.error('No role selected');\n      return;\n    }\n    setLoading(true);\n    try {\n      const activePermissions = getActivePermissions();\n\n      // Convert permissions array to object format for backend\n      const permissionsObject = {};\n      activePermissions.forEach(permissionId => {\n        permissionsObject[permissionId] = true;\n      });\n\n      // Update role with new permissions\n      const roleData = {\n        role_name: selectedRole.role_name,\n        role_description: selectedRole.role_description,\n        permissions: permissionsObject\n      };\n      const result = await rbacService.updateRole(selectedRole.id, roleData);\n      if (result.success) {\n        toast.success('Permissions updated successfully');\n        return activePermissions;\n      } else {\n        toast.error(result.error || 'Failed to update permissions');\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Save permissions error:', error);\n      toast.error('Failed to save permissions');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    permissions,\n    togglePermission,\n    savePermissions,\n    getActivePermissions,\n    loading\n  };\n};\n_s(usePermissions, \"2fA/h+gEhPuF6xEGPKpZYJYhxl8=\");", "map": {"version": 3, "names": ["useState", "useEffect", "PERMISSION_CATEGORIES", "rbacService", "toast", "usePermissions", "selectedR<PERSON>", "_s", "permissions", "setPermissions", "loading", "setLoading", "initializePermissions", "rolePermissions", "permissionState", "Array", "isArray", "Object", "values", "for<PERSON>ach", "category", "permission", "id", "includes", "togglePermission", "permissionId", "prev", "getActivePermissions", "entries", "filter", "_", "isActive", "map", "savePermissions", "error", "activePermissions", "permissionsObject", "roleData", "role_name", "role_description", "result", "updateRole", "success", "Error", "console"], "sources": ["D:/R-NeuroIQ-Admin-Portal/src/hooks/usePermissions.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PERMISSION_CATEGORIES } from '../data/permissionsData';\nimport rbacService from '../services/rbacService';\nimport { toast } from 'react-toastify';\n\nexport const usePermissions = (selectedRole) => {\n  const [permissions, setPermissions] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    if (selectedRole) {\n      initializePermissions();\n    }\n  }, [selectedRole]);\n\n  const initializePermissions = () => {\n    if (!selectedRole) {\n      setPermissions({});\n      return;\n    }\n\n    const rolePermissions = selectedRole.permissions || {};\n    const permissionState = {};\n\n    // Handle both array format (frontend) and object format (backend)\n    if (Array.isArray(rolePermissions)) {\n      // Frontend format - array of permission IDs\n      Object.values(PERMISSION_CATEGORIES).forEach(category => {\n        category.permissions.forEach(permission => {\n          permissionState[permission.id] = rolePermissions.includes(permission.id);\n        });\n      });\n    } else {\n      // Backend format - object with permission keys\n      Object.values(PERMISSION_CATEGORIES).forEach(category => {\n        category.permissions.forEach(permission => {\n          permissionState[permission.id] = rolePermissions[permission.id] === true;\n        });\n      });\n    }\n\n    setPermissions(permissionState);\n  };\n\n  const togglePermission = (permissionId) => {\n    setPermissions(prev => ({\n      ...prev,\n      [permissionId]: !prev[permissionId]\n    }));\n  };\n\n  const getActivePermissions = () => {\n    return Object.entries(permissions)\n      .filter(([_, isActive]) => isActive)\n      .map(([permissionId]) => permissionId);\n  };\n\n  const savePermissions = async () => {\n    if (!selectedRole) {\n      toast.error('No role selected');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const activePermissions = getActivePermissions();\n\n      // Convert permissions array to object format for backend\n      const permissionsObject = {};\n      activePermissions.forEach(permissionId => {\n        permissionsObject[permissionId] = true;\n      });\n\n      // Update role with new permissions\n      const roleData = {\n        role_name: selectedRole.role_name,\n        role_description: selectedRole.role_description,\n        permissions: permissionsObject\n      };\n\n      const result = await rbacService.updateRole(selectedRole.id, roleData);\n\n      if (result.success) {\n        toast.success('Permissions updated successfully');\n        return activePermissions;\n      } else {\n        toast.error(result.error || 'Failed to update permissions');\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Save permissions error:', error);\n      toast.error('Failed to save permissions');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    permissions,\n    togglePermission,\n    savePermissions,\n    getActivePermissions,\n    loading\n  };\n};"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,OAAO,MAAMC,cAAc,GAAIC,YAAY,IAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACd,IAAIK,YAAY,EAAE;MAChBM,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACN,YAAY,CAAC,CAAC;EAElB,MAAMM,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACN,YAAY,EAAE;MACjBG,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,MAAMI,eAAe,GAAGP,YAAY,CAACE,WAAW,IAAI,CAAC,CAAC;IACtD,MAAMM,eAAe,GAAG,CAAC,CAAC;;IAE1B;IACA,IAAIC,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE;MAClC;MACAI,MAAM,CAACC,MAAM,CAAChB,qBAAqB,CAAC,CAACiB,OAAO,CAACC,QAAQ,IAAI;QACvDA,QAAQ,CAACZ,WAAW,CAACW,OAAO,CAACE,UAAU,IAAI;UACzCP,eAAe,CAACO,UAAU,CAACC,EAAE,CAAC,GAAGT,eAAe,CAACU,QAAQ,CAACF,UAAU,CAACC,EAAE,CAAC;QAC1E,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,MAAM,CAACC,MAAM,CAAChB,qBAAqB,CAAC,CAACiB,OAAO,CAACC,QAAQ,IAAI;QACvDA,QAAQ,CAACZ,WAAW,CAACW,OAAO,CAACE,UAAU,IAAI;UACzCP,eAAe,CAACO,UAAU,CAACC,EAAE,CAAC,GAAGT,eAAe,CAACQ,UAAU,CAACC,EAAE,CAAC,KAAK,IAAI;QAC1E,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAb,cAAc,CAACK,eAAe,CAAC;EACjC,CAAC;EAED,MAAMU,gBAAgB,GAAIC,YAAY,IAAK;IACzChB,cAAc,CAACiB,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACD,YAAY,GAAG,CAACC,IAAI,CAACD,YAAY;IACpC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAOV,MAAM,CAACW,OAAO,CAACpB,WAAW,CAAC,CAC/BqB,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,CAAC,CACnCC,GAAG,CAAC,CAAC,CAACP,YAAY,CAAC,KAAKA,YAAY,CAAC;EAC1C,CAAC;EAED,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC3B,YAAY,EAAE;MACjBF,KAAK,CAAC8B,KAAK,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEAvB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMwB,iBAAiB,GAAGR,oBAAoB,CAAC,CAAC;;MAEhD;MACA,MAAMS,iBAAiB,GAAG,CAAC,CAAC;MAC5BD,iBAAiB,CAAChB,OAAO,CAACM,YAAY,IAAI;QACxCW,iBAAiB,CAACX,YAAY,CAAC,GAAG,IAAI;MACxC,CAAC,CAAC;;MAEF;MACA,MAAMY,QAAQ,GAAG;QACfC,SAAS,EAAEhC,YAAY,CAACgC,SAAS;QACjCC,gBAAgB,EAAEjC,YAAY,CAACiC,gBAAgB;QAC/C/B,WAAW,EAAE4B;MACf,CAAC;MAED,MAAMI,MAAM,GAAG,MAAMrC,WAAW,CAACsC,UAAU,CAACnC,YAAY,CAACgB,EAAE,EAAEe,QAAQ,CAAC;MAEtE,IAAIG,MAAM,CAACE,OAAO,EAAE;QAClBtC,KAAK,CAACsC,OAAO,CAAC,kCAAkC,CAAC;QACjD,OAAOP,iBAAiB;MAC1B,CAAC,MAAM;QACL/B,KAAK,CAAC8B,KAAK,CAACM,MAAM,CAACN,KAAK,IAAI,8BAA8B,CAAC;QAC3D,MAAM,IAAIS,KAAK,CAACH,MAAM,CAACN,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C9B,KAAK,CAAC8B,KAAK,CAAC,4BAA4B,CAAC;MACzC,MAAMA,KAAK;IACb,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLH,WAAW;IACXgB,gBAAgB;IAChBS,eAAe;IACfN,oBAAoB;IACpBjB;EACF,CAAC;AACH,CAAC;AAACH,EAAA,CApGWF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}